import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, RelationPaths, Relations, RequestContext, Transaction} from '@vendure/core';
import {ForumReview} from '../entities';
import {ForumReviewAuditInput} from '../generated-admin-types';
import {ForumReviewOperate} from '../permission-definition';
import {ForumReviewService} from '../service';
@Resolver('ForumReview')
export class ForumReviewAdminResolver {
  constructor(private forumPostService: ForumReviewService) {}

  // 查询评论
  @Query()
  @Allow(ForumReviewOperate.Read)
  async forumReviews(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumReview>,
    @Relations({entity: ForumReview})
    relations: RelationPaths<ForumReview>,
  ) {
    return this.forumPostService.findAll(ctx, options, relations);
  }

  // 查询评论详情
  @Query()
  @Allow(ForumReviewOperate.Read)
  async forumReview(@Ctx() ctx: RequestContext, @Args('forumReviewId') forumReviewId: ID) {
    return this.forumPostService.findOne(ctx, forumReviewId);
  }

  // 评论审核
  @Transaction()
  @Mutation()
  @Allow(ForumReviewOperate.Update)
  async reviewForumAudit(@Ctx() ctx: RequestContext, @Args('input') input: ForumReviewAuditInput) {
    return this.forumPostService.reviewForumAudit(ctx, input);
  }

  // 删除评论
  @Transaction()
  @Mutation()
  @Allow(ForumReviewOperate.Delete)
  async deleteForumReview(@Ctx() ctx: RequestContext, @Args('forumReviewId') forumReviewId: ID) {
    return this.forumPostService.deleteForumReview(ctx, forumReviewId);
  }
}
