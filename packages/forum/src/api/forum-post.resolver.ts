import {Args, Mutation, Parent, Query, ResolveField, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, Permission, RequestContext, Transaction} from '@vendure/core';
import {ForumPost} from '../entities';
import {ForumActivityStatus, ForumPostHotDateType, ForumPostInput} from '../generated-admin-types';
import {ForumActivityService, ForumPostService, ForumReviewService} from '../service';

@Resolver('ForumPost')
export class ForumPostResolver {
  constructor(
    private forumPostService: ForumPostService,
    private forumReviewService: ForumReviewService,
    private forumActivityService: ForumActivityService,
  ) {}

  // 发布帖子
  @Mutation()
  @Allow(Permission.Owner)
  async upsertForumPost(@Ctx() ctx: RequestContext, @Args('input') input: ForumPostInput) {
    return this.forumPostService.upsertForumPost(ctx, input);
  }

  // 查询待审核的帖子
  @Query()
  @Allow(Permission.Owner)
  async getUnAuditForumPosts(@Ctx() ctx: RequestContext, @Args('options') options: ListQueryOptions<ForumPost>) {
    return this.forumPostService.getUnAuditForumPosts(ctx, options);
  }

  // 查询最热的帖子
  @Query()
  @Allow(Permission.Public)
  async getHotForumPosts(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('forumPostHotDateType') forumPostHotDateType: ForumPostHotDateType,
    @Args('isWish') isWish = false,
  ) {
    // return this.forumPostService.getHotForumPosts(ctx, options, forumPostHotDateType, isWish);
    return this.forumPostService.getTopicsIncludePinned(ctx, options, 'hot', isWish);
  }

  // 查询最新的帖子
  @Query()
  @Allow(Permission.Public)
  async getNewForumPosts(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('isWish') isWish = false,
  ) {
    // return this.forumPostService.getNewForumPosts(ctx, options, isWish);
    return this.forumPostService.getTopicsIncludePinned(ctx, options, 'new', isWish);
  }

  // 查看帖子详情
  @Query()
  @Allow(Permission.Public)
  async getForumTopicByPid(@Ctx() ctx: RequestContext, @Args('id') id: string) {
    return this.forumPostService.getForumTopicByPostId(ctx, id);
  }

  // 取消点赞
  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  forumDownPost(@Ctx() ctx: RequestContext, @Args('pid') pid: string) {
    return this.forumPostService.forumDownPost(ctx, pid);
  }

  // 点赞
  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  forumUpPost(@Ctx() ctx: RequestContext, @Args('pid') pid: string) {
    return this.forumPostService.forumUpPost(ctx, pid);
  }

  // 根据话题查看最热的帖子
  @Query()
  @Allow(Permission.Public)
  async getHotForumPostsByTag(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('forumTagId') forumTagId: ID,
    @Args('forumPostHotDateType') forumPostHotDateType: ForumPostHotDateType,
    @Args('isWish') isWish = false,
  ) {
    return this.forumPostService.getHotForumPostsByTag(ctx, options, forumTagId, forumPostHotDateType, isWish);
  }

  // 根据话题查看最新的帖子
  @Query()
  @Allow(Permission.Public)
  async getNewForumPostsByTag(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('forumTagId') forumTagId: ID,
    @Args('isWish') isWish = false,
  ) {
    return this.forumPostService.getNewForumPostsByTag(ctx, options, forumTagId, isWish);
  }

  @Transaction()
  @Mutation()
  @Allow(Permission.Owner)
  deleteForumPost(@Ctx() ctx: RequestContext, @Args('forumPostId') forumPostId: ID) {
    return this.forumPostService.deleteForumPost(ctx, forumPostId);
  }

  // 获取自己发布的帖子
  @Query()
  @Allow(Permission.Owner)
  async getMyForumPosts(@Ctx() ctx: RequestContext, @Args('options') options: ListQueryOptions<ForumPost>) {
    return this.forumPostService.getMyForumPosts(ctx, options);
  }

  // 获取自己评论数和被点赞数
  @Query()
  @Allow(Permission.Owner)
  async getMyForumPostCount(@Ctx() ctx: RequestContext) {
    return this.forumPostService.getMyForumPostCount(ctx);
  }

  // 根据活动查询最最热的帖子
  @Query()
  @Allow(Permission.Public)
  async getHotForumPostsByActivity(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('activityId') activityId: ID,
    @Args('forumPostHotDateType') forumPostHotDateType: ForumPostHotDateType,
    @Args('isWish') isWish = false,
  ) {
    return this.forumPostService.getHotForumPostsByActivity(ctx, options, activityId, forumPostHotDateType, isWish);
  }

  // 根据活动查询最新的帖子
  @Query()
  @Allow(Permission.Public)
  async getNewForumPostsByActivity(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ForumPost>,
    @Args('activityId') activityId: ID,
    @Args('isWish') isWish = false,
  ) {
    return this.forumPostService.getNewForumPostsByActivity(ctx, options, activityId, isWish);
  }

  // 帖子分享
  @Transaction()
  @Mutation()
  @Allow(Permission.Public)
  forumPostShare(@Ctx() ctx: RequestContext, @Args('id') id: ID) {
    return this.forumPostService.forumPostShare(ctx, id);
  }

  // 话题关联活动
  @ResolveField()
  forumActivities(@Ctx() ctx: RequestContext, @Parent() forumPost: ForumPost) {
    if (forumPost.forumActivities && forumPost.forumActivities.length > 0) {
      // 过滤掉已经结束的活动
      forumPost.forumActivities = forumPost.forumActivities.filter(
        forumActivity =>
          forumActivity.status === ForumActivityStatus.Normal &&
          forumActivity.endTime > new Date() &&
          forumActivity.startTime <= new Date(),
      );
      return forumPost.forumActivities;
    }
    return [];
  }
}
