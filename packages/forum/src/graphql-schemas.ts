import {gql} from 'graphql-tag';
// eslint-disable-next-line
const _scalar = gql`
  scalar DateTime
`;

const sharedTypes = gql`
  enum ForumTagStatus {
    active
    inactive
  }

  # 论坛话题
  type ForumTag implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    deletedAt: DateTime
    name: String!
    remark: String
    description: String
    image: String
    smallProgramQRCodeLink: String
    status: ForumTagStatus!
    postCount: Int
    replyCount: Int
    maxUpvoteCount: Int
    forumTagActivity: ForumActivity
    sort: Int
    tagHash: String
    shareTitle: String
    shareImg: String
    visitorsCount: Int
    pageViews: Int
  }

  type ForumTagList implements PaginatedList {
    items: [ForumTag!]!
    totalItems: Int!
  }

  input ForumTagInput {
    id: ID
    name: String!
    remark: String
    description: String
    image: String
    status: ForumTagStatus!
    sort: Int
    shareTitle: String
    shareImg: String
  }

  enum ForumActivityStatus {
    # 未开始
    notStarted
    # 进行中
    normal
    # 已结束
    haveEnded
    # 已失效
    failure
  }

  # 论坛活动
  type ForumActivity implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    deletedAt: DateTime
    name: String!
    remark: String
    description: String
    smallProgramQRCodeLink: String
    status: ForumActivityStatus!
    startTime: DateTime
    endTime: DateTime
    forumTag: ForumTag
    forumTagId: ID
    postCount: Int
    replyCount: Int
    maxUpvoteCount: Int
    tagHash: String
  }

  type ForumActivityList implements PaginatedList {
    items: [ForumActivity!]!
    totalItems: Int!
  }

  input ForumActivityInput {
    id: ID
    name: String!
    remark: String
    description: String
    startTime: DateTime!
    endTime: DateTime!
    forumTagId: ID!
  }

  # 论坛用户
  type ForumCustomer implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    deletedAt: DateTime
    name: String!
    email: String
    forumUid: String
    headPortrait: String
    phone: String
    createType: ForumCustomerCreateType
    customer: Customer
    customerId: ID
  }

  type ForumCustomerList implements PaginatedList {
    items: [ForumCustomer!]!
    totalItems: Int!
  }

  enum ForumCustomerCreateType {
    admin
    user
  }

  input ForumCustomerInput {
    id: ID
    name: String!
    headPortrait: String
    phone: String
  }

  enum ForumPostType {
    # 长文
    long
    # 短文
    short
  }

  # 论坛帖子
  type ForumPost implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    deletedAt: DateTime
    type: ForumPostType
    title: String!
    content: String
    images: [String]
    mainImage: String
    forumWishPost: ForumPost
    status: ForumPostStatus
    releaseTime: DateTime
    auditTime: DateTime
    refuseReason: String
    forumCustomer: ForumCustomer
    forumCustomerId: ID
    forumTags: [ForumTag]
    forumActivities: [ForumActivity]
    products: [Product]
    tid: Int
    isWish: Boolean
    downVotes: Int
    upVotes: Int
    votes: Int
    viewCount: Int
    mainPid: Int
    postCount: Int
    tags: [String]
    upVoted: Boolean
    nodeBBPostId: Int
    nodeBBTopicId: Int
    uid: Int
    shareCount: Int
    shareTitle: String
    shareImg: String
    visitorsCount: Int
    pageViews: Int
    pinned: Boolean
  }

  enum ForumPostStatus {
    # 草稿
    draft
    # 待审核
    pending
    # 已发布
    published
    # 审核拒绝
    refused
  }

  type ForumPostList implements PaginatedList {
    items: [ForumPost!]!
    totalItems: Int!
  }

  input ForumPostInput {
    id: ID
    title: String!
    content: String!
    images: [String]
    mainImage: String
    forumCustomerId: ID
    forumTagIds: [ID]
    productIds: [ID]
    wishPostId: ID
    type: ForumPostType!
    isPublish: Boolean
    shareTitle: String
    shareImg: String
  }

  enum ForumPostAuditStatus {
    # 审核通过
    pass
    # 审核拒绝
    refuse
  }

  input ForumPostAuditInput {
    forumPostId: ID!
    status: ForumPostAuditStatus!
    refuseReason: String
  }

  enum ForumPostHotDateType {
    # 日
    daily
    # 周
    weekly
    # 月
    monthly
    # 总
    alltime
  }

  type ForumTopicNodeBB {
    # 帖子ID
    tid: Int
    # 点踩数
    downVotes: Int
    # 点赞数
    upVotes: Int
    # 总点赞数
    votes: Int
    # 浏览数
    viewCount: Int
    # 主贴ID
    mainPid: Int
    # 帖子数(回复数)
    postCount: Int
    # 话题
    tags: [String]
    # 帖子标题
    title: String
    # 帖子主图
    mainImage: String
    # 作者
    user: ForumUser
    # 内容
    content: String
    # 帖子图片组
    images: [String]
    # 帖子类型
    type: ForumPostType
    # 是否心愿帖
    isWish: Boolean
  }

  type ForumUser {
    # 用户ID
    uid: Int
    # 用户名
    username: String
    # 头像
    picture: String
  }

  # 操作响应
  type OperationResponse {
    code: String
    message: String
  }

  type ForumVote {
    operation: OperationResponse
    upVoted: Boolean
  }

  # 回复
  type ForumReplies {
    uid: Int
    pid: Int
    tid: Int
    votes: Int
    upVotes: Int
    downVotes: Int
    user: ForumUser
    upVoted: Boolean
    downVoted: Boolean
    repliesContent: String
    repliesTime: DateTime
    toPid: Int
    parent: ForumRepliesParentUser
    repliesCount: Int
  }

  type ForumRepliesParentUser {
    displayname: String
    username: String
  }

  # 通知类型
  enum NotificationType {
    upvote
    reply
    mention
    all
  }

  # 通知数据
  type ForumNotification {
    bodyLong: String
    bodyShort: String
    datetime: String
    datetimeISO: String
    from: Int
    image: String
    importance: Int
    mergeId: String
    nid: String
    path: String
    pid: Int
    read: Boolean
    readClass: String
    tid: Int
    topicTitle: String
    type: String
    user: ForumUser
    forumCustomer: ForumCustomer
    reviewId: Int
    forumPostId: Int
  }

  enum ForumReviewStatus {
    # 待审核
    pending
    # 审核通过
    pass
    # 审核拒绝
    refuse
  }

  type ForumReview implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    deletedAt: DateTime
    content: String
    images: [String]
    status: ForumReviewStatus
    auditTime: DateTime
    refuseReason: String
    forumPostId: String
    forumPost: ForumPost
    postId: String
    toPostId: String
    topicId: String
    forumCustomer: ForumCustomer
    parentForumCustomer: ForumCustomer
    upVotes: Int
    reviewTime: DateTime
    level: Int
    reviewCount: Int
    upVoted: Boolean
    reviewUid: String
    parentPostId: Int
  }

  type ForumReviewList implements PaginatedList {
    items: [ForumReview!]!
    totalItems: Int!
  }

  # generated by generateListOptions function
  input ForumReviewListOptions

  input ForumNotificationListOptions {
    skip: Int
    take: Int
  }

  type ForumNotificationList {
    items: [ForumNotification]
    totalItems: Int
  }

  extend type Query {
    forumTags(options: ForumTagListOptions): ForumTagList!
    forumTag(forumTagId: ID!): ForumTag

    forumActivities(options: ForumActivityListOptions): ForumActivityList!
    forumActivity(forumActivityId: ID!): ForumActivity

    forumCustomers(options: ForumCustomerListOptions): ForumCustomerList!
    forumCustomer(forumCustomerId: ID!): ForumCustomer

    forumPosts(options: ForumPostListOptions, forumTagId: ID, forumActivityId: ID): ForumPostList!
    forumPost(forumPostId: ID!): ForumPost

    getUnAuditForumPosts(options: ForumPostListOptions): ForumPostList!

    getHotForumPosts(
      options: ForumPostListOptions
      forumPostHotDateType: ForumPostHotDateType!
      isWish: Boolean
    ): ForumPostList
    getNewForumPosts(options: ForumPostListOptions): ForumPostList

    getForumTopicByPid(id: String!): ForumPost

    # 查看评论
    getForumReviews(pid: String!, level: Int, options: ForumReviewListOptions): ForumReviewList

    # 根据话题ID查询最热的帖子
    getHotForumPostsByTag(
      options: ForumPostListOptions
      forumTagId: ID!
      forumPostHotDateType: ForumPostHotDateType
      isWish: Boolean
    ): ForumPostList
    # 根据话题ID查询最新的帖子
    getNewForumPostsByTag(options: ForumPostListOptions, forumTagId: ID!): ForumPostList

    # 根据活动ID查询最热的帖子
    getHotForumPostsByActivity(
      options: ForumPostListOptions
      activityId: ID!
      forumPostHotDateType: ForumPostHotDateType
      isWish: Boolean
    ): ForumPostList
    # 根据活动ID查询最新的帖子
    getNewForumPostsByActivity(options: ForumPostListOptions, activityId: ID!, isWish: Boolean): ForumPostList

    # 根据tag或者activity的tagHash查询最热的帖子
    getHotForumPostsByTagHash(
      options: ForumPostListOptions
      forumTagHash: String
      forumPostHotDateType: ForumPostHotDateType
      isWish: Boolean
    ): ForumPostList

    # 查询通知
    forumNotifications(notificationType: NotificationType, options: ForumNotificationListOptions): ForumNotificationList
    # 查询是否有未读通知
    hasUnreadNotifications(notificationType: NotificationType): Boolean

    # 查看自己论坛信息
    getMyForumPostCount: MyForumPostCount
    # 查看自己发布的帖子
    getMyForumPosts(options: ForumPostListOptions): ForumPostList

    forumReviews(options: ForumReviewListOptions): ForumReviewList!
    forumReview(forumReviewId: ID!): ForumReview

    getForumCustomer: ForumCustomer
  }

  type MyForumPostCount {
    topicCount: Int
    postCount: Int
    reputation: Int
  }

  input ForumReviewAuditInput {
    forumReviewId: ID!
    status: ForumPostAuditStatus!
    refuseReason: String
  }

  extend type Mutation {
    # 新增或更新话题
    upsertForumTag(input: ForumTagInput!): ForumTag!
    # 删除话题
    deleteForumTag(forumTagId: ID!): DeletionResponse!
    # 发布或失效话题
    updateForumTagStatus(forumTagId: ID!, status: ForumTagStatus!): ForumTag!

    # 新增或更新活动
    upsertForumActivity(input: ForumActivityInput!): ForumActivity!
    # 删除活动
    deleteForumActivity(forumActivityId: ID!): DeletionResponse!
    # 失效活动
    updateForumActivityStatus(forumActivityId: ID!, status: ForumActivityStatus!): ForumActivity!

    # 新增或更新用户
    upsertForumCustomer(input: ForumCustomerInput!): ForumCustomer!
    # 删除用户
    deleteForumCustomer(forumCustomerId: ID!): DeletionResponse!

    # 新增或更新帖子
    upsertForumPost(input: ForumPostInput!): ForumPost!
    # 删除帖子
    deleteForumPost(forumPostId: ID!): DeletionResponse!
    # 审核帖子
    auditForumPost(input: ForumPostAuditInput!): ForumPost!

    # 取消点赞
    forumDownPost(pid: String!): ForumVote
    # 点赞
    forumUpPost(pid: String!): ForumVote
    # 评论
    forumReviewPost(reviewId: ID, postId: ID, content: String!, images: [String]): ForumReview

    # 审核评论
    reviewForumAudit(input: ForumReviewAuditInput!): ForumReview!

    # 删除评论
    deleteForumReview(forumReviewId: ID!): DeletionResponse!

    # 通知已读
    markNotificationsAsRead(notificationType: NotificationType): Boolean

    # 帖子分享
    forumPostShare(id: ID!): ForumPost

    # 帖子置顶
    forumPostPin(forumPostId: ID!, isPinned: Boolean!): ForumPost
  }

  # generated by generateListOptions function
  input ForumTagListOptions

  # generated by generateListOptions function
  input ForumActivityListOptions

  # generated by generateListOptions function
  input ForumCustomerListOptions

  # generated by generateListOptions function
  input ForumPostListOptions
`;
export const shopSchemaExtensions = gql`
  ${sharedTypes}
`;

export const adminSchemaExtensions = gql`
  ${sharedTypes}
`;
