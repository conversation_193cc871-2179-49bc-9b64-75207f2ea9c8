import {Injectable} from '@nestjs/common';
import {CacheService, DEFAULT_CACHE_TIMEOUT, MemoryStorageService} from '@scmally/kvs';
import {
  ChannelService,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {ForumActivity, ForumTag} from '../entities';
import {DeletionResult, ForumTagInput, ForumTagStatus} from '../generated-admin-types';
import {NodeBBForumTopic} from '../node-bb.types';
import {NodeBBApiClient} from './forum-node-bb-api.client';
import {InterfaceForumMatomo} from './interface-forum-matomo';
import {NodeBBService} from './node-bb.service';
@Injectable()
export class ForumTagService {
  private interfaceForumMatomo: InterfaceForumMatomo;
  constructor(
    private nodeBBApiClient: NodeBBApiClient,
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private nodeBBService: NodeBBService,
    private memoryStorageService: MemoryStorageService,
    private cacheService: CacheService,
  ) {}

  registerMatomo(interfaceForumMatomo: InterfaceForumMatomo) {
    this.interfaceForumMatomo = interfaceForumMatomo;
  }

  async findByIds(ctx: RequestContext, tagIds: ID[]) {
    if (!tagIds || tagIds?.length <= 0) {
      return [];
    }
    const qb = this.connection.getRepository(ctx, ForumTag).createQueryBuilder();
    qb.andWhere(`${qb.alias}.id IN (:...tagIds)`, {tagIds});
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    return qb.getMany();
  }
  findOne(
    ctx: RequestContext,
    forumTagId: ID,
    options?: ListQueryOptions<ForumTag>,
    relations?: RelationPaths<ForumTag>,
  ): ForumTag | PromiseLike<ForumTag | null> | null {
    const qb = this.listQueryBuilder.build(ForumTag, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.id = :forumTagId`, {forumTagId});
    return qb.take(1).getOne();
  }
  async findAll(ctx: RequestContext, options: ListQueryOptions<ForumTag>, relations: RelationPaths<ForumTag>) {
    const qb = this.listQueryBuilder.build(ForumTag, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    if (ctx.apiType === 'shop') {
      qb.andWhere(`${qb.alias}.status = :status`, {status: ForumTagStatus.Active});
    }
    qb.orderBy(`${qb.alias}.sort`, 'DESC');
    const [items, totalItems] = await qb.getManyAndCount();
    if (items.length > 0 && ctx.apiType === 'admin') {
      const forumTagAllVisitors = await this.interfaceForumMatomo.getCustomPageDataStatistics(
        ctx,
        new Date(0),
        new Date(),
        `ForumActivity`,
      );
      items.forEach(item => {
        const forumTagVisitors = forumTagAllVisitors.find(visitor => idsAreEqual(visitor.pageId, item.id));
        if (forumTagVisitors) {
          item.visitorsCount = forumTagVisitors.visits;
          item.pageViews = forumTagVisitors.hits;
        } else {
          item.visitorsCount = 0;
          item.pageViews = 0;
        }
      });
    }
    return {
      items,
      totalItems,
    };
  }

  async upsertForumTag(ctx: RequestContext, input: ForumTagInput) {
    // 验证参数
    await this.validInput(ctx, input);
    let forumTagId: ID;
    if (input.id) {
      // 更新
      await this.connection.getRepository(ctx, ForumTag).update(
        {
          id: input.id,
        },
        {
          name: input.name,
          remark: input.remark || '',
          description: input.description || '',
          image: input.image || '',
          status: input.status,
          sort: input.sort || 0,
          shareImg: input.shareImg || '',
          shareTitle: input.shareTitle || '',
        },
      );
      await this.cacheService.removeCache(`Query:findAllActiveForumTags:${ctx.channelId}`);
      return this.findOne(ctx, input.id);
    } else {
      // 创建
      let forumTag = new ForumTag({
        name: input.name,
        remark: input.remark || '',
        description: input.description || '',
        image: input.image || '',
        status: input.status,
        tagHash: this.nodeBBApiClient.generateUniqueHash(),
        sort: input.sort || 0,
        shareImg: input.shareImg || '',
        shareTitle: input.shareTitle || '',
      });
      forumTag = await this.channelService.assignToCurrentChannel(forumTag, ctx);
      forumTag = await this.connection.getRepository(ctx, ForumTag).save(forumTag);
      forumTagId = forumTag.id;
    }
    await this.cacheService.removeCache(`Query:findAllActiveForumTags:${ctx.channelId}`);
    return this.findOne(ctx, forumTagId);
  }
  async validInput(ctx: RequestContext, input: ForumTagInput) {
    if (!input.name) {
      throw new Error('话题名称不能为空');
    }
    if (input.name.length > 50) {
      throw new Error('话题名称不能超过20个字符');
    }

    if (input.remark && input.remark.length > 100) {
      throw new Error('话题备注不能超过100个字符');
    }

    if (!input.description) {
      throw new Error('话题描述不能为空');
    }

    if (input.description.length > 150) {
      throw new Error('话题描述不能超过150个字符');
    }
    if (!input.image) {
      throw new Error('话题图片不能为空');
    }
  }

  async deleteForumTag(ctx: RequestContext, forumTagId: ID) {
    const forumTag = await this.findOne(ctx, forumTagId);
    if (!forumTag) {
      throw new Error('论坛标签不存在');
    }
    await this.connection.getRepository(ctx, ForumTag).update(
      {
        id: forumTagId,
      },
      {
        deletedAt: new Date(),
      },
    );
    await this.cacheService.removeCache(`Query:findAllActiveForumTags:${ctx.channelId}`);
    return {
      result: DeletionResult.Deleted,
    };
  }

  async updateForumTagStatus(ctx: RequestContext, forumTagId: ID, status: ForumTagStatus) {
    const forumTag = await this.findOne(ctx, forumTagId);
    if (!forumTag) {
      throw new Error('论坛标签不存在');
    }
    if (forumTag.status === status) {
      return forumTag;
    }
    await this.connection.getRepository(ctx, ForumTag).update(
      {
        id: forumTagId,
      },
      {
        status,
      },
    );
    await this.cacheService.removeCache(`Query:findAllActiveForumTags:${ctx.channelId}`);
    return this.findOne(ctx, forumTagId);
  }

  // 获取全部活跃的论坛标签 不分页
  async findAllActiveForumTags(ctx: RequestContext) {
    let memoryStorageCacheKey = ``;
    if (ctx.apiType === 'shop') {
      memoryStorageCacheKey = `Query:findAllActiveForumTags:${ctx.channelId}`;
      const tags = this.memoryStorageService.get(memoryStorageCacheKey);
      if (tags) {
        return tags;
      }
    }
    const qb = this.connection.getRepository(ctx, ForumTag).createQueryBuilder();
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.status = :status`, {status: ForumTagStatus.Active});
    if (ctx.apiType === 'shop') {
      qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
    }
    const allActiveForumTags = await qb.getMany();
    if (ctx.apiType === 'shop') {
      this.memoryStorageService.set(memoryStorageCacheKey, allActiveForumTags);
    }
    return allActiveForumTags;
  }

  async maxUpvoteCount(ctx: RequestContext, tagHash: string) {
    const tag = (await this.nodeBBService.getTagByHash(tagHash)) as {
      topics: NodeBBForumTopic[];
    };
    const upVotes = (tag?.topics as NodeBBForumTopic[])[0]?.upvotes ?? 0;
    return upVotes;
  }
  async getReplyCount(ctx: RequestContext, tagHash: string) {
    const tag = await this.nodeBBService.getTagByHash(tagHash);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (tag as any).totalPostCount;
  }
  async getPostCount(ctx: RequestContext, tagHash: string) {
    const tag = await this.nodeBBService.getTagByHash(tagHash);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (tag as any).totalTopicCount;
  }

  async getTagPostCount(ctx: RequestContext, forumTag: ForumTag) {
    let memoryStorageCacheKey = '';
    if (ctx.apiType === 'shop') {
      memoryStorageCacheKey = `Query:forumTagPostCount:${forumTag.id}:${ctx.channelId}`;
      const cache = await this.memoryStorageService.get(memoryStorageCacheKey);
      if (cache) {
        return cache.postCount;
      }
    }
    const postCount = await this.getPostCount(ctx, forumTag.tagHash);
    if (ctx.apiType === 'shop') {
      await this.connection.rawConnection.queryResultCache?.getFromCache({
        identifier: memoryStorageCacheKey,
        duration: DEFAULT_CACHE_TIMEOUT,
        query: memoryStorageCacheKey,
        result: {postCount},
      });
      this.memoryStorageService.set(memoryStorageCacheKey, {postCount});
    }
    return postCount;
  }

  async getActivityPostCount(ctx: RequestContext, forumActivity: ForumActivity) {
    let memoryStorageCacheKey = '';
    if (ctx.apiType === 'shop') {
      memoryStorageCacheKey = `Query:forumActivityPostCount:${forumActivity.id}:${ctx.channelId}`;
      const cache = await this.memoryStorageService.get(memoryStorageCacheKey);
      if (cache) {
        return cache.postCount;
      }
    }
    const postCount = await this.getPostCount(ctx, forumActivity.tagHash);
    if (ctx.apiType === 'shop') {
      await this.connection.rawConnection.queryResultCache?.getFromCache({
        identifier: memoryStorageCacheKey,
        duration: DEFAULT_CACHE_TIMEOUT,
        query: memoryStorageCacheKey,
        result: {postCount},
      });
      this.memoryStorageService.set(memoryStorageCacheKey, {postCount});
    }
    return postCount;
  }
}
