import {Injectable} from '@nestjs/common';
import {RequestContext, TransactionalConnection} from '@vendure/core';
import {WeappDynamicRouteConfig} from '../entities';
import {WeappDynamicRouteConfigInput} from '../generated-shop-types';
@Injectable()
export class WeappDynamicRouteConfigService {
  constructor(
    private connection: TransactionalConnection, // private productCustomService: ProductCustomService, // private virtualCurrencyService: VirtualCurrencyService,
  ) {}

  async getWeappDynamicRouteConfig(ctx: RequestContext) {
    const channelId = ctx.channelId;
    const qb = this.connection
      .getRepository(ctx, WeappDynamicRouteConfig)
      .createQueryBuilder('weappDynamicRouteConfig');
    qb.where(`${qb.alias}.channelId = :channelId`, {channelId});
    return qb.getOne();
  }

  async upsertWeappDynamicRouteConfig(ctx: RequestContext, input: WeappDynamicRouteConfigInput) {
    const channelId = ctx.channelId;
    const tabbar = input.tabbarConfig;
    let weappDynamicRouteConfig = await this.getWeappDynamicRouteConfig(ctx);
    if (weappDynamicRouteConfig) {
      await this.connection.getRepository(ctx, WeappDynamicRouteConfig).update(
        {id: weappDynamicRouteConfig.id},
        {
          tabbarConfig: input.tabbarConfig,
          eventTrackingConfig: input.eventTrackingConfig,
          channelId: channelId,
        },
      );
    } else {
      weappDynamicRouteConfig = new WeappDynamicRouteConfig({
        tabbarConfig: tabbar ?? undefined,
        eventTrackingConfig: input.eventTrackingConfig,
        channelId: channelId,
      });
      await this.connection.getRepository(ctx, WeappDynamicRouteConfig).save(weappDynamicRouteConfig);
    }
    return this.getWeappDynamicRouteConfig(ctx);
  }
}
