import {Injectable} from '@nestjs/common';
import {WeChatAuthService} from '@scmally/wechat';
import {CustomerService, ID, Logger, RequestContext, TransactionalConnection} from '@vendure/core';
import axios from 'axios';
import {DateTime} from 'luxon';
import {TemplateConfig, UserCoupon} from '../entities';
import {ShareData, ShareType} from '../generated-admin-types';
import {CommonService} from './common.service';
import {WeappDynamicRouteConfigService} from './weapp-dynamic-route-config.service';
@Injectable()
export class SendMessageService {
  private readonly sendMessageUrl =
    'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={{access_token}}'; // 发送订阅消息
  constructor(
    private weChatAuthService: WeChatAuthService,
    private customerService: CustomerService,
    private commonService: CommonService,
    private connection: TransactionalConnection,
    private weAppDynamicRouteConfigService: WeappDynamicRouteConfigService,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async sendMessage(ctx: RequestContext, customerId: ID, templateId: string, data: any, page?: string) {
    const sendMessageUrl = await this.getSendMessageUrl(ctx);
    const customer = await this.customerService.findOne(ctx, customerId);
    if (!customer?.phoneNumber) {
      Logger.error('用户手机号不存在', 'SendMessageService.sendMessage', 'sendMessage');
      return;
    }
    const wechatUser = await this.weChatAuthService.getUserByPhone(ctx, customer.phoneNumber);
    if (!wechatUser?.openId) {
      Logger.error('用户openId不存在', 'SendMessageService.sendMessage', 'sendMessage');
      return;
    }
    const message = {
      touser: wechatUser.openId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      template_id: templateId,
      page,
      data,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      miniprogram_state: process.env.MINI_PROGRAM_VERSION,
      lang: 'zh_CN',
    };
    /** 发送请求 */
    const response = await axios.post(sendMessageUrl, message);
    return response;
  }

  async getSendMessageUrl(ctx: RequestContext) {
    const wechatAccessToken = await this.weChatAuthService.getAccessToken(ctx);
    return this.sendMessageUrl.replace('{{access_token}}', wechatAccessToken);
  }

  async claimCouponNotification(ctx: RequestContext, customerId: ID, userCoupon: UserCoupon) {
    //TODO 优惠券领取成功通知 暂时注释
    return;
    // const templateId = process.env.WECHAT_CLAIM_COUPON_TEMPLATE_ID;
    // if (!templateId) {
    //   Logger.error('模板id不存在', 'SendMessageService.claimCouponNotification', 'claimCouponNotification');
    //   return;
    // }
    // const data = {
    //   thing1: {
    //     value: userCoupon.coupon.name,
    //   },
    //   time3: {
    //     value: new Date().toLocaleTimeString(),
    //   },
    //   time4: {
    //     value: userCoupon.maturityAt?.toLocaleTimeString(),
    //   },
    //   thing5: {
    //     value: '领券成功！请尽快使用',
    //   },
    // };
    // return this.sendMessage(ctx, customerId, templateId, data);
  }

  async couponExpiredNotice(ctx: RequestContext, customerId: ID, userCoupon: UserCoupon) {
    //TODO 优惠券过期通知 暂时注释
    return;
    // const templateId = process.env.WECHAT_COUPON_EXPIRED_TEMPLATE_ID;
    // if (!templateId) {
    //   Logger.error('模板id不存在', 'SendMessageService.couponExpiredNotice', 'couponExpiredNotice');
    //   return;
    // }
    // const data = {
    //   thing2: {
    //     value: userCoupon.coupon.name,
    //   },
    //   time4: {
    //     value: `${userCoupon.claimAt?.toLocaleTimeString()}~${userCoupon.maturityAt?.toLocaleTimeString()}`,
    //   },
    //   thing3: {
    //     value: '您的优惠券即将过期',
    //   },
    // };
    // return this.sendMessage(ctx, customerId, templateId, data);
  }
  /**
   * 
   * activityName: Maybe<Scalars['String']>;
  activityContent: Maybe<Scalars['String']>;
  warmPrompt: Maybe<Scalars['String']>;
  jumpType: Maybe<JumpType>;
  jumpValue: Maybe<Scalars['String']>;
   */
  async couponGiveNotice(
    ctx: RequestContext,
    customerId: ID,
    activityName: string,
    activityContent: string,
    warmPrompt: string,
    jumpType: string,
    jumpValue: string,
  ) {
    // 优惠券发放通知
    const templateId = process.env.WECHAT_COUPON_GRANT_TEMPLATE_ID;
    if (!templateId) {
      Logger.error('模板id不存在', 'SendMessageService.couponExpiredNotice', 'couponExpiredNotice');
      return;
    }
    let data = {};
    if (process.env.APP_ENV === 'production') {
      data = {
        thing10: {
          value: activityName,
        },
        thing11: {
          value: activityContent,
        },
        thing15: {
          value: warmPrompt,
        },
      };
    } else {
      data = {
        thing11: {
          value: activityName,
        },
        thing4: {
          value: activityContent,
        },
        thing5: {
          value: warmPrompt,
        },
      };
    }
    const shareData: ShareData = {
      shareType: jumpType as ShareType,
      shareValue: jumpValue,
      isDetail: true,
    };
    const shareStr = await this.commonService.generateShareDataStr(ctx, shareData);
    // 通知挑战到活动页面需告知是根据活动详情ID查询 IsDetail为true
    // const page = `pages/home/<USER>
    const page = `pages/home/<USER>
    return this.sendMessage(ctx, customerId, templateId, data, page);
  }

  async blindBoxActivityRemind(
    ctx: RequestContext,
    customerId: ID,
    activityName: string,
    startTime: Date,
    jumpType: string,
    jumpValue: string,
    templateConfig: TemplateConfig,
  ) {
    // 动态组装模板数据
    const data = Object.keys(templateConfig.fields).reduce((result: Record<string, {value: string}>, key: string) => {
      const fieldValue = templateConfig.fields[key];
      if (fieldValue === '活动名称') {
        result[key] = {value: activityName};
      } else if (fieldValue === '开始时间') {
        result[key] = {value: DateTime.fromJSDate(startTime).toFormat('yyyy-MM-dd HH:mm:ss')};
      } else {
        result[key] = {value: ''}; // 默认空值，防止缺失字段报错
      }
      return result;
    }, {});

    const shareData: ShareData = {
      shareType: jumpType as ShareType,
      shareValue: jumpValue,
      // isDetail: true,
    };
    const shareStr = await this.commonService.generateShareDataStr(ctx, shareData);
    // 通知挑战到活动页面需告知是根据活动详情ID查询 IsDetail为true
    // const page = `pages/home/<USER>
    const page = `pages/home/<USER>
    return this.sendMessage(ctx, customerId, templateConfig.templateId, data, page);
  }

  /**
   * 购物金发放通知
   * @param ctx 请求上下文
   * @param templateConfig 模板配置
   * @param orderCode 订单ID
   * @param customerID 用户ID
   * @param amount 购物金金额
   * @param page 跳转路径
   */
  async sendShoppingCreditsNotification(
    ctx: RequestContext,
    templateConfig: TemplateConfig,
    orderCode: string,
    customerID: string,
    amount: number,
  ) {
    try {
      const templateId = templateConfig.templateId;
      if (!templateId) {
        Logger.error('购物金发放模板ID不存在', 'SendMessageService.sendShoppingCreditsNotification');
        return {success: false, message: '模板ID不存在'};
      }
      const data = Object.keys(templateConfig.fields).reduce((result: Record<string, {value: string}>, key: string) => {
        const fieldValue = templateConfig.fields[key];
        if (fieldValue === '购物金金额') {
          result[key] = {value: (amount / 100).toFixed(2)}; // 转换为元
        } else if (fieldValue === '订单号') {
          result[key] = {value: orderCode};
        } else if (fieldValue === '发放时间') {
          result[key] = {value: DateTime.now().toFormat('yyyy-MM-dd HH:mm:ss')}; // 发放购物金时间
        } else if (fieldValue === '备注') {
          result[key] = {value: '💰️购物金当钱花! 点击兑换正装商品>>'};
        } else {
          result[key] = {value: ''}; // 默认空值，防止缺失字段报错
        }
        return result;
      }, {});
      const pageID = (
        await this.weAppDynamicRouteConfigService.getWeappDynamicRouteConfig(ctx)
      )?.eventTrackingConfig?.find(f => f.eventScheme === 'shoppingCreditCenter')?.routeValue;
      const page = `pages/subpack-main/pages/activity/index?id=${pageID}`;
      const response = await this.sendMessage(ctx, customerID, templateId, data, page);

      if (response?.data?.errcode === 0) {
        Logger.info(
          `购物金发放通知发送成功，订单ID: ${orderCode}`,
          'SendMessageService.sendShoppingCreditsNotification',
        );
        return {success: true, message: '通知发送成功', response: response.data};
      } else {
        Logger.error(
          `购物金发放通知发送失败，订单ID: ${orderCode}，错误: ${JSON.stringify(response?.data)}`,
          'SendMessageService.sendShoppingCreditsNotification',
        );
        return {success: false, message: '通知发送失败', response: response?.data};
      }
    } catch (error) {
      Logger.error(
        `购物金发放通知发送异常，订单ID: ${orderCode}，错误: ${error.message}`,
        'SendMessageService.sendShoppingCreditsNotification',
      );
      return {success: false, message: `通知发送异常: ${error.message}`};
    }
  }
}
