import {Injectable} from '@nestjs/common';
import {
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  PaginatedList,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
} from '@vendure/core';
import {SubscriptionMessageRecord} from '../entities/subscription-message-record.entity';

export interface CreateSubscriptionMessageRecordInput {
  customerId: ID;
  relatedType: string;
  relatedID: string;
  channelId?: ID;
}

@Injectable()
export class SubscriptionMessageRecordService {
  constructor(private connection: TransactionalConnection, private listQueryBuilder: ListQueryBuilder) {}

  async create(ctx: RequestContext, input: CreateSubscriptionMessageRecordInput): Promise<SubscriptionMessageRecord> {
    const record = new SubscriptionMessageRecord({
      customer: {id: input.customerId},
      relatedType: input.relatedType,
      relatedID: input.relatedID,
      isSend: 0,
      channelId: input.channelId || ctx.channelId,
    });

    return this.connection.getRepository(ctx, SubscriptionMessageRecord).save(record);
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<SubscriptionMessageRecord>,
    relations?: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<PaginatedList<SubscriptionMessageRecord>> {
    return this.listQueryBuilder
      .build(SubscriptionMessageRecord, options, {
        ctx,
        relations: relations ?? ['customer'],
        channelId: ctx.channelId,
      })
      .getManyAndCount()
      .then(([items, totalItems]) => ({
        items,
        totalItems,
      }));
  }

  async findOne(
    ctx: RequestContext,
    id: ID,
    relations?: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<SubscriptionMessageRecord | undefined> {
    return this.connection
      .getRepository(ctx, SubscriptionMessageRecord)
      .findOne({
        where: {id},
        relations: relations ?? ['customer'],
      })
      .then(result => result || undefined);
  }

  async findByRelated(
    ctx: RequestContext,
    relatedType: string,
    relatedID: string,
  ): Promise<SubscriptionMessageRecord | undefined> {
    return this.connection
      .getRepository(ctx, SubscriptionMessageRecord)
      .findOne({
        where: {
          relatedType,
          relatedID,
          channelId: ctx.channelId,
        },
        relations: ['customer'],
      })
      .then(result => result || undefined);
  }

  async updateSendStatus(ctx: RequestContext, id: ID, isSend: number): Promise<SubscriptionMessageRecord> {
    await this.connection.getRepository(ctx, SubscriptionMessageRecord).update(id, {isSend});

    return this.findOne(ctx, id).then(result => {
      if (!result) {
        throw new Error(`SubscriptionMessageRecord with id ${id} not found`);
      }
      return result;
    });
  }
}
