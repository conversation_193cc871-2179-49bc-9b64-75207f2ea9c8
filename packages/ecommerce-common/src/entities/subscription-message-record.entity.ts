import {Customer, DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, ManyToOne} from 'typeorm';

@Entity()
export class SubscriptionMessageRecord extends VendureEntity {
  constructor(input?: DeepPartial<SubscriptionMessageRecord>) {
    super(input);
  }

  @ManyToOne(type => Customer)
  customer: Customer;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联类型'})
  relatedType: string;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联ID'})
  relatedID: string;

  @Index()
  @Column({type: 'tinyint', default: 0, comment: '是否发送，0表示未发送，1表示已发送'})
  isSend: number;

  @Index()
  @Column({type: 'varchar', nullable: true, comment: '渠道ID'})
  channelId: ID;
}
