import {Customer, DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index, ManyToOne, Unique} from 'typeorm';

@Entity()
@Unique('UNIQUE_USER_PER_ORG', ['targetType', 'targetId'])
export class SubscriptionMessageRecord extends VendureEntity {
  constructor(input?: DeepPartial<SubscriptionMessageRecord>) {
    super(input);
  }

  @ManyToOne(type => Customer)
  customer: Customer;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联类型'})
  targetType: string;

  @Index()
  @Column({type: 'varchar', length: 50, comment: '关联ID'})
  targetId: string;

  @Column({type: 'boolean', default: false, comment: '是否发送，0表示未发送，1表示已发送'})
  sended: boolean;

  @Column({type: 'varchar', nullable: true, comment: '渠道ID'})
  channelId: ID;
}
