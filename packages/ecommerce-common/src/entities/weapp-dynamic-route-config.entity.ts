import {DeepPartial, ID, VendureEntity} from '@vendure/core';
import {Column, Entity, Index} from 'typeorm';
import {WeappEventTrackingRouteConfig, WeappTabbarRouteConfig} from '../generated-shop-types';

@Entity()
export class WeappDynamicRouteConfig extends VendureEntity {
  constructor(input?: DeepPartial<WeappDynamicRouteConfig>) {
    super(input);
  }

  @Column({type: 'simple-json', nullable: true})
  tabbarConfig: WeappTabbarRouteConfig[] | null;

  @Column({type: 'simple-json', nullable: true})
  eventTrackingConfig: WeappEventTrackingRouteConfig[] | null;

  @Index()
  @Column({nullable: false, type: 'int', comment: '渠道id'})
  channelId: ID;
}
