import {Query, Args, Mutation, Resolver} from '@nestjs/graphql';
import {Ctx, RequestContext, Allow, Relations, RelationPaths, Transaction, Permission} from '@vendure/core';
import {WeappDynamicRouteConfig} from '../entities';
import {WeappDynamicRouteConfigInput} from '../generated-shop-types';
import {WeappDynamicRouteConfigOperate} from '../permission-definition';
import {WeappDynamicRouteConfigService} from '../service';

@Resolver('WeappDynamicRouteConfigAdminResolver')
export class WeappDynamicRouteConfigAdminResolver {
  constructor(private weappDynamicRouteConfigService: WeappDynamicRouteConfigService) {}

  @Query()
  @Allow(WeappDynamicRouteConfigOperate.Read)
  getWeappDynamicRouteConfig(
    @Ctx() ctx: RequestContext,
    @Relations({entity: WeappDynamicRouteConfig})
    relations: RelationPaths<WeappDynamicRouteConfig>,
  ) {
    return this.weappDynamicRouteConfigService.getWeappDynamicRouteConfig(ctx);
  }

  @Transaction()
  @Mutation()
  @Allow(WeappDynamicRouteConfigOperate.Create, WeappDynamicRouteConfigOperate.Update)
  async upsertWeappDynamicRouteConfig(@Ctx() ctx: RequestContext, @Args('input') input: WeappDynamicRouteConfigInput) {
    return this.weappDynamicRouteConfigService.upsertWeappDynamicRouteConfig(ctx, input);
  }
}

@Resolver('WeappDynamicRouteConfigResolver')
export class WeappDynamicRouteConfigResolver {
  constructor(private weappDynamicRouteConfigService: WeappDynamicRouteConfigService) {}

  @Query()
  @Allow(Permission.Owner)
  getWeappDynamicRouteConfig(
    @Ctx() ctx: RequestContext,
    // @Relations({entity: WeappDynamicRouteConfig})
    // relations: RelationPaths<WeappDynamicRouteConfig>,
  ) {
    return this.weappDynamicRouteConfigService.getWeappDynamicRouteConfig(ctx);
  }
}
