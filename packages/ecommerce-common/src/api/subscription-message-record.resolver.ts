import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {
  Allow,
  Ctx,
  ID,
  ListQueryOptions,
  PaginatedList,
  Permission,
  RelationPaths,
  Relations,
  RequestContext,
  Transaction,
} from '@vendure/core';
import {SubscriptionMessageRecord} from '../entities/subscription-message-record.entity';
import {SubscriptionMessageRecordService} from '../service/subscription-message-record.service';

export interface CreateSubscriptionMessageRecordInput {
  orderID: string;
  customerID: string;
}

export interface CreateSubscriptionMessageRecordResult {
  success: boolean;
  message: string;
}

@Resolver('SubscriptionMessageRecord')
export class SubscriptionMessageRecordResolver {
  constructor(private subscriptionMessageRecordService: SubscriptionMessageRecordService) {}

  @Allow(Permission.Public)
  @Mutation()
  @Transaction()
  async createShoppingCreditsSubscriptionRecord(
    @Ctx() ctx: RequestContext,
    @Args('input') input: CreateSubscriptionMessageRecordInput,
  ): Promise<CreateSubscriptionMessageRecordResult> {
    try {
      // 验证请求参数
      if (!input.orderID || !input.customerID) {
        return {
          success: false,
          message: '订单ID和用户ID不能为空',
        };
      }

      // 检查是否已存在相同的订阅记录
      const existingRecord = await this.subscriptionMessageRecordService.findByRelated(
        ctx,
        'shoppingCredits',
        input.orderID,
      );

      if (existingRecord) {
        return {
          success: false,
          message: '该订单的购物金发放订阅记录已存在',
        };
      }

      // 创建新的订阅记录
      await this.subscriptionMessageRecordService.create(ctx, {
        customerId: input.customerID,
        relatedType: 'shoppingCredits',
        relatedID: input.orderID,
        channelId: ctx.channelId,
      });

      return {
        success: true,
        message: '购物金发放订阅记录创建成功',
      };
    } catch (error) {
      return {
        success: false,
        message: `创建订阅记录失败: ${error.message}`,
      };
    }
  }

  @Allow(Permission.ReadCatalog)
  @Query()
  async subscriptionMessageRecords(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<SubscriptionMessageRecord>,
    @Relations({entity: SubscriptionMessageRecord})
    relations: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<PaginatedList<SubscriptionMessageRecord>> {
    return this.subscriptionMessageRecordService.findAll(ctx, options, relations);
  }

  @Allow(Permission.ReadCatalog)
  @Query()
  async subscriptionMessageRecord(
    @Ctx() ctx: RequestContext,
    @Args('id') id: ID,
    @Relations({entity: SubscriptionMessageRecord})
    relations: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<SubscriptionMessageRecord | undefined> {
    return this.subscriptionMessageRecordService.findOne(ctx, id, relations);
  }
}
