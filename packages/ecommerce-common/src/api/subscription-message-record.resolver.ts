import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {
  Allow,
  Ctx,
  CustomerService,
  ID,
  ListQueryOptions,
  PaginatedList,
  Permission,
  RelationPaths,
  Relations,
  RequestContext,
  Transaction,
} from '@vendure/core';
import {SubscriptionMessageRecord} from '../entities/subscription-message-record.entity';
import {TemplateType} from '../generated-shop-types';
import {SubscriptionMessageRecordService} from '../service/subscription-message-record.service';

export interface CreateSubscriptionMessageRecordResolverInput {
  orderId: ID;
}

@Resolver('SubscriptionMessageRecord')
export class SubscriptionMessageRecordResolver {
  constructor(
    private subscriptionMessageRecordService: SubscriptionMessageRecordService,
    private customerService: CustomerService,
  ) {}

  @Allow(Permission.Authenticated)
  @Mutation()
  @Transaction()
  async createShoppingCreditsSubscriptionRecord(
    @Ctx() ctx: RequestContext,
    @Args('orderId') orderId: ID,
  ): Promise<boolean> {
    console.log('开始创建');
    const customer = ctx.activeUserId ? await this.customerService.findOneByUserId(ctx, ctx.activeUserId) : null;
    try {
      // 验证请求参数
      if (!orderId || !customer) {
        return false;
      }

      // 检查是否已存在相同的订阅记录
      const existingRecord = await this.subscriptionMessageRecordService.findByTarget(
        ctx,
        TemplateType.ShoppingCreditsNotification,
        orderId as string,
      );

      if (existingRecord) {
        return false;
      }

      // 创建新的订阅记录
      await this.subscriptionMessageRecordService.create(ctx, {
        customerId: customer.id,
        targetType: TemplateType.ShoppingCreditsNotification,
        targetId: orderId as string,
        channelId: ctx.channelId,
      });

      return true;
    } catch (error) {
      return false;
    }
  }

  @Allow(Permission.ReadCatalog)
  @Query()
  async subscriptionMessageRecords(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<SubscriptionMessageRecord>,
    @Relations({entity: SubscriptionMessageRecord})
    relations: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<PaginatedList<SubscriptionMessageRecord>> {
    return this.subscriptionMessageRecordService.findAll(ctx, options, relations);
  }

  @Allow(Permission.ReadCatalog)
  @Query()
  async subscriptionMessageRecord(
    @Ctx() ctx: RequestContext,
    @Args('id') id: ID,
    @Relations({entity: SubscriptionMessageRecord})
    relations: RelationPaths<SubscriptionMessageRecord>,
  ): Promise<SubscriptionMessageRecord | undefined> {
    return this.subscriptionMessageRecordService.findOne(ctx, id, relations);
  }
}
