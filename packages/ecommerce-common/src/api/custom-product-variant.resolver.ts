import {Args, Parent, Query, ResolveField, Resolver} from '@nestjs/graphql';
import {
  QueryProductVariantArgs,
  QueryProductVariantsArgs,
  StockMovementListOptions,
} from '@vendure/common/lib/generated-types';
import {DEFAULT_CHANNEL_CODE} from '@vendure/common/lib/shared-constants';
import {
  Allow,
  Api,
  ApiType,
  Asset,
  Channel,
  Ctx,
  CurrencyCode,
  FacetValue,
  PaginatedList,
  Permission,
  Product,
  ProductOption,
  ProductVariant,
  ProductVariantService,
  RelationPaths,
  Relations,
  RequestContext,
  RequestContextCacheService,
  StockLevel,
  StockLevelService,
  StockMovement,
  StockMovementService,
  TaxRate,
  Translated,
  idsAreEqual,
} from '@vendure/core';
import {CustomerProductService, CustomerProductVariantService, PointsProductService} from '../service';
import {LocaleStringHydrator} from '../service/custom-locale-string-hydrator';
@Resolver('ProductVariant')
export class CustomProductVariantEntityResolver {
  constructor(
    private customerProductVariantService: CustomerProductVariantService,
    private customerProductService: CustomerProductService,
    private requestContextCache: RequestContextCacheService,
    private localeStringHydrator: LocaleStringHydrator,
    private pointsProductService: PointsProductService,
  ) {}

  @ResolveField()
  async name(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, productVariant, 'name');
  }

  @ResolveField()
  async languageCode(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<string> {
    return this.localeStringHydrator.hydrateLocaleStringField(ctx, productVariant, 'languageCode');
  }

  @ResolveField()
  async price(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<number> {
    return this.customerProductVariantService.hydratePriceFields(ctx, productVariant, 'price');
  }

  @ResolveField()
  async priceWithTax(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<number> {
    return this.customerProductVariantService.hydratePriceFields(ctx, productVariant, 'priceWithTax');
  }

  @ResolveField()
  async currencyCode(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<CurrencyCode> {
    return this.customerProductVariantService.hydratePriceFields(ctx, productVariant, 'currencyCode');
  }

  @ResolveField()
  async taxRateApplied(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<TaxRate> {
    return this.customerProductVariantService.hydratePriceFields(ctx, productVariant, 'taxRateApplied');
  }

  @ResolveField()
  async product(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<Product | undefined> {
    if (productVariant.product?.name) {
      return productVariant.product;
    }

    return this.requestContextCache.get(ctx, `ProductVariantEntityResolver.product(${productVariant.productId})`, () =>
      this.customerProductVariantService.getProductForVariant(ctx, productVariant),
    );
  }

  @ResolveField()
  async assets(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<Asset[] | undefined> {
    return this.customerProductService.getEntityAssets(ctx, productVariant, 'ProductVariant');
  }

  @ResolveField()
  async stockLevel(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<string> {
    return this.customerProductVariantService.getDisplayStockLevel(ctx, productVariant);
  }
  @ResolveField()
  async featuredAsset(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
  ): Promise<Asset | undefined> {
    if (productVariant.featuredAsset) {
      return productVariant.featuredAsset;
    }
    return this.customerProductService.getFeaturedAsset(ctx, productVariant, 'ProductVariant');
  }

  @ResolveField()
  async options(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
  ): Promise<Array<Translated<ProductOption>>> {
    if (productVariant.options) {
      return productVariant.options as Array<Translated<ProductOption>>;
    }
    return this.customerProductVariantService.getOptionsForVariant(ctx, productVariant.id);
  }

  @ResolveField()
  async facetValues(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
    @Api() apiType: ApiType,
  ): Promise<Array<Translated<FacetValue>>> {
    if (productVariant.facetValues?.length === 0) {
      return [];
    }
    let facetValues: Array<Translated<FacetValue>>;
    if (productVariant.facetValues?.[0]?.channels) {
      facetValues = productVariant.facetValues as Array<Translated<FacetValue>>;
    } else {
      facetValues = await this.customerProductVariantService.getFacetValuesForVariant(ctx, productVariant.id);
    }

    return facetValues.filter(fv => {
      if (!fv.channels.find(c => idsAreEqual(c.id, ctx.channelId))) {
        return false;
      }
      if (apiType === 'shop' && fv.facet.isPrivate) {
        return false;
      }
      return true;
    });
  }

  @ResolveField()
  async pointExchange(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant) {
    return this.pointsProductService.getSkuPointsExchange(ctx, productVariant);
  }
}

@Resolver('ProductVariant')
export class CustomProductVariantResolver {
  constructor(private customerProductVariantService: CustomerProductVariantService) {}
  @ResolveField('isThereAnyStock')
  async isThereAnyStock(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant) {
    const {stockOnHand, stockAllocated} = await this.customerProductVariantService.getAvailableStock(
      ctx,
      productVariant,
    );
    return stockOnHand - stockAllocated - productVariant.outOfStockThreshold > 0;
  }
}

@Resolver('ProductVariant')
export class CustomProductVariantAdminResolver {
  constructor(
    private stockLevelService: StockLevelService,
    private stockMovementService: StockMovementService,
    private customerProductVariantService: CustomerProductVariantService,
    private productVariantService: ProductVariantService,
  ) {}

  @ResolveField()
  async stockMovements(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
    @Args() args: {options: StockMovementListOptions},
  ): Promise<PaginatedList<StockMovement>> {
    if (productVariant.stockMovements?.length) {
      let {take, skip} = args.options;
      if (!take) {
        take = 10;
      }
      if (!skip) {
        skip = 0;
      }
      return {
        items: productVariant.stockMovements.slice(skip, skip + take),
        totalItems: productVariant.stockMovements.length,
      };
    } else {
      return this.stockMovementService.getStockMovementsByProductVariantId(ctx, productVariant.id, args.options);
    }
  }
  @ResolveField('stockOnHand')
  async stockOnHand(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
    @Args() args: {options: StockMovementListOptions},
  ): Promise<number> {
    const {stockOnHand} = await this.customerProductVariantService.getAvailableStock(ctx, productVariant);
    return stockOnHand;
  }
  @ResolveField('stockAllocated')
  async stockAllocated(
    @Ctx() ctx: RequestContext,
    @Parent() productVariant: ProductVariant,
    @Args() args: {options: StockMovementListOptions},
  ): Promise<number> {
    const {stockAllocated} = await this.customerProductVariantService.getAvailableStock(ctx, productVariant);
    return stockAllocated;
  }

  @ResolveField('channels')
  async channels(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<Channel[]> {
    const isDefaultChannel = ctx.channel.code === DEFAULT_CHANNEL_CODE;
    let channels = [];
    if (productVariant.channels?.length) {
      channels = productVariant.channels;
    } else {
      channels = await this.productVariantService.getProductVariantChannels(ctx, productVariant.id);
    }
    return channels.filter(channel => (isDefaultChannel ? true : idsAreEqual(channel.id, ctx.channelId)));
  }

  @ResolveField('stockLevels')
  async stockLevels(@Ctx() ctx: RequestContext, @Parent() productVariant: ProductVariant): Promise<StockLevel[]> {
    if (productVariant.stockLevels?.length) {
      return productVariant.stockLevels;
    } else {
      return this.stockLevelService.getStockLevelsForVariant(ctx, productVariant.id);
    }
  }
  @Query()
  @Allow(Permission.ReadCatalog, Permission.ReadProduct)
  async productVariants(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryProductVariantsArgs,
    @Relations({entity: ProductVariant}) relations: RelationPaths<ProductVariant>,
  ): Promise<PaginatedList<Translated<ProductVariant>>> {
    if (args.productId) {
      relations = this.customerProductVariantService.addProductVariantRelations(relations);
      return this.productVariantService.getVariantsByProductId(
        ctx,
        args.productId,
        args.options || undefined,
        relations,
      );
    }
    return this.productVariantService.findAll(ctx, args.options || undefined);
  }

  @Query()
  @Allow(Permission.ReadCatalog, Permission.ReadProduct)
  async productVariant(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryProductVariantArgs,
  ): Promise<Translated<ProductVariant> | undefined> {
    return this.productVariantService.findOne(ctx, args.id);
  }
}
