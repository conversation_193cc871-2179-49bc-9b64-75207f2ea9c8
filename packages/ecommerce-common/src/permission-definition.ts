import {CrudPermissionDefinition, PermissionDefinition} from '@vendure/core';

export const BannerOperate = new PermissionDefinition({
  name: 'BannerOperate',
  description: 'Operation management banner',
});
export const CustomPageOperate = new PermissionDefinition({
  name: 'CustomPageOperate',
  description: 'Customize page operation permissions',
});
export const HotWordOperate = new PermissionDefinition({
  name: 'HotWordOperate',
  description: 'Hot Word Operate Relevant authority ',
});
export const AnnouncementOperate = new PermissionDefinition({
  name: 'AnnouncementOperate',
  description: 'Announcement Operate Relevant authority ',
});
export const CouponOperate = new PermissionDefinition({
  name: 'CouponOperate',
  description: 'Discount paper operation ',
});

// 分享推广权限
export const ShareOperate = new PermissionDefinition({
  name: 'ShareOperate',
  description: '分享推广权限',
});

export const CouponPermission = new CrudPermissionDefinition('Coupon');

export const CouponFailureOperate = new PermissionDefinition({
  name: 'CouponFailureOperate',
  description: '优惠券失效操作权限',
});

export const UserCouponPermission = new CrudPermissionDefinition('UserCoupon');

export const PurchasePremiumOperate = new PermissionDefinition({
  name: 'PurchasePremiumOperate',
  description: 'Discount paper PurchasePremium ',
});
export const DistributorOperate = new PermissionDefinition({
  name: 'DistributorOperate',
  description: 'The distributor manages authority',
});

export const DistributorPermission = new CrudPermissionDefinition('Distributor');

//设置分销员
export const SetCustomerDistributorPermission = new PermissionDefinition({
  name: 'SetCustomerDistributor',
  description: '设置用户的分销员',
});

export const SelectiveGiftActivityOperate = new CrudPermissionDefinition('SelectiveGiftActivity');

export const DiscountActivityOperate = new PermissionDefinition({
  name: 'DiscountActivityOperate',
  description: 'Discount activity management authority',
});
export const FullDiscountPresentOperate = new PermissionDefinition({
  name: 'FullDiscountPresentOperate',
  description: 'Full discount gift management authority',
});
export const FreeGiftOperate = new PermissionDefinition({
  name: 'FreeGiftOperate',
  description: 'Free gift management authority',
});
export const SettingOperate = new PermissionDefinition({
  name: 'SettingOperate',
  description: 'Setting management authority',
});

export const StatisticsQuery = new PermissionDefinition({
  name: 'Statistics',
  description: 'Statistics management authority',
});

export const GroupCustomerOperate = new PermissionDefinition({
  name: 'GroupCustomerOperate',
  description: 'Group customer management authority',
});

export const OperationPlanOperate = new PermissionDefinition({
  name: 'OperationPlanOperate',
  description: 'Operation plan management authority',
});

export const PackageDiscountOperate = new PermissionDefinition({
  name: 'PackageDiscountOperate',
  description: '打包一口价活动管理权限',
});
export const MerchantVoluntaryRefundPermission = new PermissionDefinition({
  name: 'MerchantVoluntaryRefund',
  description: '商家主动退款权限',
});

export const TransactionCustomerStatisticsPermission = new PermissionDefinition({
  name: 'TransactionCustomerStatistics',
  description: '交易客户统计权限',
});

export const UMengConfigPermission = new PermissionDefinition({
  name: 'UMengConfig',
  description: '友盟配置权限',
});
export const MemberPricePermission = new CrudPermissionDefinition('MemberPrice');

export const GiftCardOrderPermission = new CrudPermissionDefinition('GiftCardOrder');

export const PaymentRewardActivityPermission = new CrudPermissionDefinition('PaymentRewardActivity');

export const ProductDataStatisticsExportFile = new PermissionDefinition({
  name: 'ProductDataStatisticsExportFile',
  description: '商品数据统计导出文件',
});

export const CustomerDataExportFile = new PermissionDefinition({
  name: 'CustomerDataExportFile',
  description: '客户数据导出文件',
});

export const MemberOrderDataExport = new PermissionDefinition({
  name: 'MemberOrderDataExport',
  description: '会员订单数据导出',
});
export const CustomerDistributionBindingExport = new PermissionDefinition({
  name: 'CustomerDistributionBindingExport',
  description: '客户分销绑定导出',
});
export const CouponBundleOperate = new CrudPermissionDefinition(
  'CouponBundle',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建优惠券包';
      case 'read':
        return '查看优惠券包';
      case 'update':
        return '编辑优惠券包';
      case 'delete':
        return '删除优惠券包';
    }
  },
);
export const FirstCustomerBenefitOperate = new CrudPermissionDefinition(
  'FirstCustomerBenefit',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建新人礼包';
      case 'read':
        return '查看新人礼包';
      case 'update':
        return '编辑新人礼包';
      case 'delete':
        return '删除新人礼包';
    }
  },
);
export const ExclusionGroupOperate = new CrudPermissionDefinition(
  'ExclusionGroup',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建商品互斥组';
      case 'read':
        return '查看商品互斥组';
      case 'update':
        return '编辑商品互斥组';
      case 'delete':
        return '删除商品互斥组';
    }
  },
);
export const PointsProductOperate = new CrudPermissionDefinition(
  'PointsProduct',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建积分商品';
      case 'read':
        return '查看积分商品';
      case 'update':
        return '编辑积分商品';
      case 'delete':
        return '删除积分商品';
    }
  },
);
export const PointsConfigOperate = new CrudPermissionDefinition(
  'PointsConfig',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建积分配置';
      case 'read':
        return '查看积分配置';
      case 'update':
        return '编辑积分配置';
      case 'delete':
        return '删除积分配置';
    }
  },
);
export const ProductRestrictionsOperate = new CrudPermissionDefinition(
  'ProductRestrictions',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建商品购买地区限制';
      case 'read':
        return '查看商品购买地区限制';
      case 'update':
        return '编辑商品购买地区限制';
      case 'delete':
        return '删除商品购买地区限制';
    }
  },
);
export const CheckinConfigOperate = new CrudPermissionDefinition(
  'CheckinConfig',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建签到配置';
      case 'read':
        return '查看签到配置';
      case 'update':
        return '编辑签到配置';
      case 'delete':
        return '删除签到配置';
    }
  },
);
export const ExportTaskOperate = new CrudPermissionDefinition(
  'ExportTask',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建导出任务';
      case 'read':
        return '查看导出任务';
      case 'update':
        return '编辑导出任务';
      case 'delete':
        return '删除导出任务';
    }
  },
);

export const BlindBoxActivityOperate = new CrudPermissionDefinition(
  'BlindBoxActivity',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建盲盒活动';
      case 'read':
        return '查看盲盒活动';
      case 'update':
        return '编辑盲盒活动';
      case 'delete':
        return '删除盲盒活动';
    }
  },
);

export const BlindBoxActivityLimitConfigOperate = new CrudPermissionDefinition(
  'BlindBoxActivityLimitConfig',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建盲盒活动限购配置';
      case 'read':
        return '查看盲盒活动限购配置';
      case 'update':
        return '编辑盲盒活动限购配置';
      case 'delete':
        return '删除盲盒活动限购配置';
    }
  },
);

export const AssistGiftOperate = new CrudPermissionDefinition(
  'AssistGift',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建助力礼包';
      case 'read':
        return '查看助力礼包';
      case 'update':
        return '编辑助力礼包';
      case 'delete':
        return '删除助力礼包';
    }
  },
);

export const BlindBoxBuyOperate = new CrudPermissionDefinition(
  'BlindBoxBuy',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建盲盒购买';
      case 'read':
        return '查看盲盒购买';
      case 'update':
        return '编辑盲盒购买';
      case 'delete':
        return '删除盲盒购买';
    }
  },
);

export const BlindBoxStatisticsOperate = new PermissionDefinition({
  name: 'BlindBoxStatisticsOperate',
  description: '盲盒统计查看权限',
});

export const FloatingWindowOperate = new CrudPermissionDefinition(
  'FloatingWindow',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建浮窗';
      case 'read':
        return '查看浮窗';
      case 'update':
        return '编辑浮窗';
      case 'delete':
        return '删除浮窗';
    }
  },
);

export const ShareSettingPermission = new CrudPermissionDefinition(
  'ShareSetting',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建分享设置';
      case 'read':
        return '查看分享设置';
      case 'update':
        return '编辑分享设置';
      case 'delete':
        return '删除分享设置';
    }
  },
);
export const ShoppingCreditsClaimActivityOperate = new CrudPermissionDefinition(
  'ShoppingCreditsClaimActivity',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建购物积分领取活动';
      case 'read':
        return '查看购物积分领取活动';
      case 'update':
        return '编辑购物积分领取活动';
      case 'delete':
        return '删除购物积分领取活动';
    }
  },
);

export const ShoppingCreditsDeductionActivityOperate = new CrudPermissionDefinition(
  'ShoppingCreditsDeductionActivity',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建购物积分抵扣活动';
      case 'read':
        return '查看购物积分抵扣活动';
      case 'update':
        return '编辑购物积分抵扣活动';
      case 'delete':
        return '删除购物积分抵扣活动';
    }
  },
);

export const ShoppingCreditsConfigOperate = new CrudPermissionDefinition(
  'ShoppingCreditsConfig',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建购物积分配置';
      case 'read':
        return '查看购物积分配置';
      case 'update':
        return '编辑购物积分配置';
      case 'delete':
        return '删除购物积分配置';
    }
  },
);

export const PersonalCenterOperate = new CrudPermissionDefinition(
  'PersonalCenter',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建个人中心页面';
      case 'read':
        return '查看个人中心页面';
      case 'update':
        return '编辑个人中心页面';
      case 'delete':
        return '删除个人中心页面';
    }
  },
);

export const ActivityCountdownOperate = new CrudPermissionDefinition(
  'ActivityCountdown',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建活动倒计时';
      case 'read':
        return '查看活动倒计时';
      case 'update':
        return '编辑活动倒计时';
      case 'delete':
        return '删除活动倒计时';
    }
  },
);

export const WeappDynamicRouteConfigOperate = new CrudPermissionDefinition(
  'WeappDynamicRouteConfig',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建小程序动态路由跳转配置';
      case 'read':
        return '查看小程序动态路由跳转配置';
      case 'update':
        return '编辑小程序动态路由跳转配置';
      case 'delete':
        return '删除小程序动态路由跳转配置';
    }
  },
);
