@import '../../global.scss';

.select-img-container {
  // min-width: 120px;
  // padding: 0 10px 10px 0;
}
.select-img {
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ccc;
  background-color: #eee;
  cursor: pointer;
  position: relative;
  img {
    // max-width: 120px;
    // max-height: 120px;
  }
}

::ng-deep image-picker.ng-invalid {
  .select-img {
    background-color: unset;
    border: 1px solid var(--color-error-300);
  }
}

.remove {
  position: absolute;
  top: 0;
  right: 0;
}

.drag-holder {
  width: 120px;
  height: 120px;
}
