<!-- image-picker.component.html -->
<div class="image-picker flex flex-wrap" cdkDropListGroup>
  <div
    class="select-img-container"
    *ngFor="let image of previewImages; let i = index"
    cdkDropList
    cdkDropListOrientation="horizontal"
    [cdkDropListData]="i"
    (cdkDropListDropped)="handleDrag($event)"
  >
    <div class="select-img" [style.width]="width" [style.height]="height" cdkDrag>
      <img [src]="image" alt="Preview Image" (click)="selectAssets(i)" />
      <clr-icon size="22" class="remove" shape="times" (click)="remove(i, $event)" *ngIf="showRemove"></clr-icon>
    </div>
  </div>

  <div
    class="select-img"
    [style.width]="width"
    [style.height]="height"
    (click)="selectAssets()"
    *ngIf="previewImages.length === 0 || multiple"
  >
    选择图片
  </div>
</div>
