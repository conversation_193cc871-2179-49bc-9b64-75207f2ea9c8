import {Component, OnInit, ChangeDetectorRef} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';

import {
  DataService,
  ModalService,
  NotificationService,
  BaseDetailComponent,
  LanguageCode,
  ServerConfigService,
} from '@vendure/admin-ui/core';

import {ORDER_DETAIL, LOGISTICS_INFO, TRANSITION_SEND_SHIPPING_ADDR} from '../../graphql/graphql';
// import {PROMOTION_RESULT_BY_ORDER_ID} from '../order/graphql';
import {
  Order as OrderType,
  OrderLine,
  Logistics,
  Fulfillment,
  // Maybe,
  // OrderCustomFields,
  OrderPromotionResult,
  MerchantVoluntaryRefund,
  FreeGiftList,
  FreeGift,
  DistributorOrder,
  OrderLinesRefundable,
} from '../../generated-admin-types';

import {OrderShippingAddressModifyDialogComponent} from '../order-shipping-address-modify-dialog/order-shipping-address-modify-dialog.component';
import {OrderShipDialogComponent} from '../order-ship-dialog/order-ship-dialog.component';
import {OrderVoluntaryRefundDialogComponent} from '../order-voluntary-refund-dialog/order-voluntary-refund-dialog.component';
import {OrderVoluntaryRefundInfoDialogComponent} from '../order/order-voluntary-refund-info-dialog/order-voluntary-refund-info-dialog.component';

import {getShippingState} from '../../commom/order-shipping-state';
import {
  isOrderAfterSaling,
  disabledWhenAfterSale,
  getAfterSaleForVoluntaryRefund,
  notAfterSalingStates,
} from '../../commom/is-order-aftersaling';

import {ClrLoadingState} from '@clr/angular';

import {FormGroup, FormBuilder} from '@angular/forms';
import {Observable} from 'rxjs';
import {LIST} from '../gift-list/graphql';
import {cloneDeep} from 'lodash';
import {DISTRIBUTOR_ORDER, GET_REFUNDABLE_AMOUNT_BY_ORDER_ID} from '../order/graphql';
import {MerchantRemarksDialogComponent} from '../order/merchant-remarks-dialog/merchant-remarks-dialog.component';
import {getTotalBlindBoxBuyPrice} from '../order/utils';
import {TokenCNYPipe} from '../../pipes/tokenCNY.pipe';
import {LabelValuesDialogComponent} from '../dialog/label-values-dialog/label-values-dialog.component';
// import {map} from 'rxjs/operators';

type OrderDetailLine = OrderLine & {proratedLineDiscount: number; isBlindBox?: boolean};
// type OrderDetailWithPromotion = Order & {
//   customFields: Maybe<OrderCustomFields & {orderPromotionResult: OrderPromotionResult}>;
// };

type FreeGiftWithCount = FreeGift & {count: number};

type Order = OrderType & {distributorOrder: DistributorOrder};

// PromotionType标签对象
export const promotionTypeObject: {[key: string]: string} = {
  member: '商品会员折扣：',
  purchaseAtAPremium: '优惠换购：',
  coupon: '优惠券：',
  discountActivity: '第X件Y折：',
  fullDiscountPresent: '满减送：',
  automaticPromotion: '自动促销：',
  packageDiscount: '打包一口价：',
  actuallyPaid: '实付满赠：',
  subscription: '订阅：',
  memberPrice: '会员价：',
  selectiveGift: '任选满赠：',
  paymentReward: '支付有礼：',
  pointsExchange: '积分兑换：',
  blindBox: '盲盒优惠折扣：',
  shoppingCreditsClaim: '购物金发放：',
  shoppingCreditsDeduction: '购物金抵扣：',
};

@Component({
  selector: 'order-detail.',
  templateUrl: 'order-detail.component.html',
  styleUrls: ['order-detail.component.scss'],
  providers: [TokenCNYPipe],
})
export class OrderDetailComponent extends BaseDetailComponent<Order> implements OnInit {
  detailForm: FormGroup;
  order$: Observable<Order>;
  id: string;
  orderInfo: Order;
  linesArr: OrderDetailLine[];
  giftLines: Array<FreeGiftWithCount> = [];
  logistics: Logistics;
  linesRefundableAmounts: OrderLinesRefundable[];
  promotionRes: OrderPromotionResult | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  discountArr: Array<any>;
  voluntaryRefundInfo: MerchantVoluntaryRefund[];
  merchantRemarks = '';
  shippingState = getShippingState;

  getLineAfterSaleState = getAfterSaleForVoluntaryRefund;
  isAfterSaling = isOrderAfterSaling;

  blindBoxBuyPrice = 0;
  isAnyLineAfterSaling = false;

  productsTotal = 0;

  isRequesting = ClrLoadingState.DEFAULT;
  distributorOrderInfo: DistributorOrder;

  get isFulfillPending() {
    const fulfill = this.orderInfo?.fulfillments;
    if (fulfill?.length) {
      return fulfill.find(item => {
        return item.state === 'Pending';
      });
    } else return false;
  }

  get getSurcharge() {
    const surcharge = this.orderInfo?.surcharges;
    const res = surcharge?.length ? surcharge[0]?.price ?? 0 : 0;
    return res;
  }

  get getShippingAddr() {
    const add = this.orderInfo?.shippingAddress;
    const d = add?.customFields?.district ? add.customFields.district : '';
    return `${add?.province}${add?.city}${d}${add?.streetLine1}${add?.streetLine2}`;
  }

  constructor(
    route: ActivatedRoute,
    router: Router,
    serverConfigService: ServerConfigService,
    protected dataService: DataService,
    private modalService: ModalService,
    private notificationService: NotificationService,
    private ref: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private tokenCny: TokenCNYPipe,
  ) {
    super(route, router, serverConfigService, dataService);
    this.detailForm = this.formBuilder.group({
      id: '',
    });
  }

  ngOnInit() {
    this.init();
    this.entity$.subscribe(res => {
      this.handleQueryDetailRes(res);
    });
    this.id = this.route.snapshot.paramMap.get('id') || '';
    // console.log(this.id);
    this.queryLogistics();
    this.getRefundableAmounts();
    // this.queryPromotionRes();

    // this.order$ = this.entity$;
    // this.order$.pipe(
    //   map(res => {
    //     console.log(res);
    //     this.orderInfo = res;
    //   }),
    // );
  }

  queryDetail() {
    this.dataService
      .query<{order: Order}>(ORDER_DETAIL, {
        id: this.id,
      })
      .mapStream(item => {
        // console.log(item);
        const res = item.order;
        return res;
      })
      .subscribe(item => {
        this.handleQueryDetailRes(item);
        this.ref.detectChanges();
      });
  }

  getRefundableAmounts() {
    this.dataService
      .query<{getRefundableAmountByOrderId: OrderLinesRefundable[]}>(GET_REFUNDABLE_AMOUNT_BY_ORDER_ID, {
        orderId: this.id,
      })
      .mapStream(res => res.getRefundableAmountByOrderId)
      .subscribe(res => {
        this.linesRefundableAmounts = res;
      });
  }

  handleQueryDetailRes(item: Order) {
    this.queryGifts(item);
    this.queryDistributorOrder(item);
    // console.log(item);
    this.orderInfo = item;
    this.linesArr = item.lines.map(line => {
      return {
        ...line,
        proratedLineDiscount: 0,
        isBlindBox: this.lineIsBlindBoxProduct(
          item?.customFields?.orderPromotionResult as OrderPromotionResult,
          line.id,
        ),
      };
    });
    this.productsTotal = this.getProductsTotal(this.orderInfo);

    if (this.linesArr.length) {
      this.isAnyLineAfterSaling = !!disabledWhenAfterSale(this.linesArr);
    }
    this.promotionRes = item?.customFields?.orderPromotionResult ?? undefined;
    this.promotionRes?.promResult?.orderLinePromResults?.forEach(lineProm => {
      const temp = this.linesArr?.find(line => {
        return line?.id === lineProm?.orderLineId;
      });
      if (temp) {
        temp.proratedLineDiscount = lineProm?.discountAmount ?? 0;
        temp.proratedLinePrice = lineProm?.totalPrice ?? 0;
        temp.quantity = lineProm?.count ?? 0;
        temp.linePrice = lineProm?.price ?? 0;
      }
    });
    this.discountArr =
      this.promotionRes?.promResult?.discountByTypes
        ?.filter(f => f?.discountAmount)
        ?.map(m => {
          return {
            label: promotionTypeObject[m!.type!],
            value: this.tokenCny.transform(m!.discountAmount ? -m!.discountAmount : 0),
          };
        }) ?? [];
    this.blindBoxBuyPrice = getTotalBlindBoxBuyPrice(item);

    this.merchantRemarks = item.customFields?.merchantRemarks ?? '';
    // console.log(this.linesArr);
  }

  queryDistributorOrder(item: Order) {
    this.dataService
      .query<{distributorOrder: DistributorOrder}>(DISTRIBUTOR_ORDER, {
        orderId: item.id,
      })
      .mapStream(res => res.distributorOrder)
      .subscribe(res => {
        this.distributorOrderInfo = res;
      });
  }

  queryGifts(item: Order) {
    const gifts = item.customFields?.orderPromotionResult?.promResult?.gifts
      ?.filter(g => g?.giftType === 'free' && g?.items?.length && g?.items?.find(f => f?.selected))
      ?.map(g => {
        const {items, ...rest} = g!;
        return {
          ...rest,
          gift: items?.find(f => f?.selected),
        };
      });
    // console.log(gifts);
    if (!gifts?.length) {
      return;
    }
    const giftIds = gifts.map(i => i?.gift?.giftId);
    // const skuIds = gifts.map(i => i?.gift?.skuId);
    if (!giftIds?.length) {
      return;
    }
    this.dataService
      .query<{freeGifts: FreeGiftList}>(LIST, {
        options: {
          filter: {
            id: {
              in: giftIds,
            },
          },
        },
      })
      .mapStream(res => res.freeGifts)
      .subscribe(res => {
        if (res?.items?.length) {
          const temp = cloneDeep(res.items) as FreeGift[];
          this.giftLines = gifts.map(g => {
            // t.product.featuredAsset = t.product.variants.find(v => skuIds.includes(v.id))?.featuredAsset;
            const t = temp.find(f => f?.id === g?.gift?.giftId);
            if (t) {
              t.product.variants = t.product.variants.filter(v => {
                return g?.gift?.skuId === v.id;
              });
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              (t as any).count = g?.gift?.count ?? 1;
              return t as FreeGiftWithCount;
            } else {
              return {name: '赠品已删除'} as FreeGiftWithCount;
            }
          });
          console.log(this.giftLines);
        }
      });
  }

  lineIsBlindBoxProduct(promRes: OrderPromotionResult, lineId: string) {
    if (!promRes) {
      return false;
    }
    const findPromLineByTypeBlindBox = promRes?.promResult?.promLineResults?.find(plr => plr?.type === 'blindBox');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const findMatchId = (findPromLineByTypeBlindBox as any)?.orderLineBlindBoxMap?.find(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (olm: any) => olm.orderLineId === lineId,
    );
    return !!findMatchId;
  }

  queryLogistics() {
    this.dataService
      .query<{logistics: Logistics}>(LOGISTICS_INFO, {
        orderId: this.id,
      })
      .mapStream(item => {
        const res = item.logistics;
        return res;
      })
      .subscribe(item => {
        // console.log(item);
        this.logistics = item;
      });
  }

  // queryPromotionRes() {
  //   this.dataService
  //     .query<{promotionResultByOrderId: OrderPromotionResult}>(PROMOTION_RESULT_BY_ORDER_ID, {
  //       orderId: this.id,
  //     })
  //     .mapStream(item => {
  //       return item.promotionResultByOrderId;
  //     })
  //     .subscribe(res => {
  //       console.log(res);
  //       this.promotionRes = res;
  //       this.promotionRes?.promResult?.orderLinePromResults?.forEach(lineProm => {
  //         const temp = this.linesArr?.find(line => {
  //           return line?.id === lineProm?.orderLineId;
  //         });
  //         if (temp) {
  //           temp.proratedLineDiscount = lineProm?.discountAmount ?? 0;
  //           temp.proratedLinePrice = lineProm?.price
  //             ? lineProm.price - temp.proratedLineDiscount
  //             : temp.proratedLinePrice;
  //         }
  //       });
  //       console.log(this.linesArr);
  //       this.ref.detectChanges();
  //     });
  // }

  getPurchasePremium(line: OrderDetailLine) {
    let res = 0;
    if (this.promotionRes) {
      const arr = this.promotionRes.promResult?.gifts;
      if (!arr) {
        return res;
      }
      for (let i = 0; i < arr?.length; i++) {
        const g = arr[i];
        if (g && g.giftType === 'mark_up') {
          const result = g.items?.find(item => item?.skuId === line.productVariant.id);
          if (result) {
            // console.log(result);
            res = result.price ?? 0;
            break;
          }
        }
      }
      return res;
    } else {
      return res;
    }
  }

  disableVoluntaryRefund(line: OrderDetailLine) {
    // console.log(line);
    // const refund = line.merchantVoluntaryRefund ?? 0;
    const afState = line.customFields?.afterSale?.state;
    const findLineRefundableAmounts = this.linesRefundableAmounts?.find(i => i.orderLine?.id === line.id);
    const refund = line.isBlindBox
      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (line as any).blindBoxRefundableAmount || (findLineRefundableAmounts?.refundableAmount ?? 0)
      : findLineRefundableAmounts?.refundableAmount ?? 0;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (this.orderInfo?.customFields as any)?.buyType === 'pointsExchange'
      ? (afState && !notAfterSalingStates.includes(afState)) ||
          this.orderInfo.state === 'Cancelled' ||
          this.orderInfo.state === 'ArrangingPayment'
      : // refund >= line.proratedLinePrice ||
        (afState && !notAfterSalingStates.includes(afState)) ||
          !refund ||
          refund <= 0 ||
          this.orderInfo.state === 'Cancelled' ||
          this.orderInfo.state === 'ArrangingPayment';
  }

  handleVoluntaryRefund(line: OrderDetailLine) {
    this.modalService
      .fromComponent(OrderVoluntaryRefundDialogComponent, {
        size: 'lg',
        locals: {
          line,
          id: this.orderInfo.id,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          buyType: (this.orderInfo?.customFields as any)?.buyType,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          orderTotalPoints: (this.promotionRes?.promResult as any)?.orderTotalPoints,
          isBlindBoxLine: line.isBlindBox,
        },
      })
      .subscribe(res => {
        if (res === 'success') {
          this.notificationService.success('主动退款成功');
        }
        this.queryDetail();
        this.getRefundableAmounts();
      });
  }

  handleClickRefundsInfo() {
    this.modalService
      .fromComponent(OrderVoluntaryRefundInfoDialogComponent, {
        size: 'lg',
        locals: {
          id: this.orderInfo.id,
        },
        closable: true,
      })
      .subscribe(res => {
        // console.log('close dialog', res);
      });
  }

  handleClickSend() {
    const item = this.orderInfo;
    this.modalService
      .fromComponent(OrderShipDialogComponent, {
        size: 'lg',
        locals: {
          id: item.id,
          code: item.code,
          shipAddr: item.shippingAddress,
          orderLines: item.lines,
          fulfill: item.fulfillments,
        },
        closable: true,
      })
      .subscribe(res => {
        if (res === 'success') {
          this.notificationService.success('发货成功');
        } else if (res === 'error') {
          this.notificationService.info('发货失败');
        }
        this.queryDetail();
        this.getRefundableAmounts();
      });
  }

  transitionSendAddr(id: string, state: string) {
    this.dataService
      .mutate<{transitionFulfillmentToState: Fulfillment}>(TRANSITION_SEND_SHIPPING_ADDR, {
        id,
        state,
      })
      .subscribe(item => {
        if (item.transitionFulfillmentToState.state === 'Shipped') {
          this.notificationService.success('发货成功');
        } else {
          this.notificationService.error('修改发货状态失败');
        }
      });
  }

  handleClickCopy() {
    this.notificationService.success('复制成功');
  }

  handleClickAddressModify() {
    this.modalService
      .fromComponent(OrderShippingAddressModifyDialogComponent, {
        size: 'lg',
        locals: {
          id: this.id,
          defaultAddr: this.orderInfo.shippingAddress,
        },
        closable: true,
      })
      .subscribe(item => {
        if (item === 'success') {
          this.notificationService.success('修改地址成功');
          this.queryDetail();
        }
      });
  }

  getProductsTotal(order: Order) {
    let sum = 0;
    order?.customFields?.orderPromotionResult?.promResult?.orderLinePromResults?.forEach(item => {
      sum += item?.price ?? 0;
    });
    return sum;
  }

  protected setFormValues(entity: Order, languageCode: LanguageCode): void {
    // throw new Error('Method not implemented.');
    this.detailForm.patchValue({
      id: entity.id,
    });
  }

  handleAddMerchantRemarks() {
    this.modalService
      .fromComponent(MerchantRemarksDialogComponent, {
        locals: {
          orderId: this.orderInfo.id,
          content: this.merchantRemarks,
        },
        size: 'md',
        closable: true,
      })
      .subscribe(res => {
        // console.log(res);
        if (res) {
          this.notificationService.success('保存成功');
          this.merchantRemarks = res;
        }
      });
  }

  checkDiscountList() {
    const total = this.promotionRes?.promResult?.discountAmount;
    this.modalService
      .fromComponent(LabelValuesDialogComponent, {
        locals: {
          title: '订单优惠金额详情',
          items: [
            ...this.discountArr,
            {
              label: '优惠总金额：',
              value: this.tokenCny.transform(total ? -total : 0),
            },
          ],
        },
        closable: true,
      })
      .subscribe();
  }
}
