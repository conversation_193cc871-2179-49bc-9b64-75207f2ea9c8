import {Component, OnInit, ChangeDetectionStrategy, Input} from '@angular/core';
import {DataService, Dialog, NotificationService} from '@vendure/admin-ui/core';
import {AUDIT_FORUM_POST, AUDIT_FORUM_REVIEW} from '../graphql';

enum AuditType {
  post = 'forumPost',
  review = 'forumReview',
}
@Component({
  selector: 'app-post-audit-dialog',
  templateUrl: './post-audit-dialog.component.html',
  styleUrls: ['./post-audit-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostAuditDialogComponent implements Dialog, OnInit {
  @Input() id: string;
  @Input() auditHere = false;
  @Input() isPass = false;
  @Input() type = 'forumPost';
  auditValue = 'pass';

  refuseReason: string;

  get showReason() {
    return this.auditHere ? this.auditValue === 'refuse' : !this.isPass;
  }

  get disableSave() {
    return this.auditHere ? this.auditValue === 'refuse' && !this.refuseReason : !this.isPass && !this.refuseReason;
  }

  constructor(private dataService: DataService, private notification: NotificationService) {}

  ngOnInit() {}

  resolveWith: (result?: boolean) => void;

  save() {
    const status = this.auditHere ? this.auditValue : this.isPass ? 'pass' : 'refuse';
    if (this.type === AuditType.post) {
      const input = {
        forumPostId: this.id,
        status,
        refuseReason: this.refuseReason,
      };
      this.dataService.mutate(AUDIT_FORUM_POST, {input}).subscribe(res => {
        if (res) {
          this.notification.success('操作成功');
          this.resolveWith(true);
        }
      });
    } else {
      const input = {
        forumReviewId: this.id,
        status,
        refuseReason: this.refuseReason,
      };
      this.dataService.mutate(AUDIT_FORUM_REVIEW, {input}).subscribe(res => {
        if (res) {
          this.notification.success('操作成功');
          this.resolveWith(true);
        }
      });
    }
  }
}
