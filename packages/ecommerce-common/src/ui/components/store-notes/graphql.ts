import gql from 'graphql-tag';
import {FORUM_ACTIVITY_FRAGMENT, FORUM_POST_FRAGMENT, FORUM_REVIEW_FRAGMENT, FORUM_TAG_FRAGMENT} from './fragment';

// 话题标签相关接口
export const FORUM_TAG_LIST = gql`
  query forumTags($options: ForumTagListOptions) {
    forumTags(options: $options) {
      items {
        ...ForumTagFragment
      }
      totalItems
    }
  }
  ${FORUM_TAG_FRAGMENT}
`;

export const FORUM_TAG_DETAIL = gql`
  query forumTag($forumTagId: ID!) {
    forumTag(forumTagId: $forumTagId) {
      ...ForumTagFragment
    }
  }
  ${FORUM_TAG_FRAGMENT}
`;

export const UPSERT_FORUM_TAG = gql`
  mutation upsertForumTag($input: ForumTagInput!) {
    upsertForumTag(input: $input) {
      id
    }
  }
`;

export const PUBLISH_FORUM_TAG = gql`
  mutation updateForumTagStatus($forumTagId: ID!, $status: ForumTagStatus!) {
    updateForumTagStatus(forumTagId: $forumTagId, status: $status) {
      id
    }
  }
`;

export const DELETE_FORUM_TAG = gql`
  mutation deleteForumTag($forumTagId: ID!) {
    deleteForumTag(forumTagId: $forumTagId) {
      result
      message
    }
  }
`;

// 话题活动相关接口
export const FORUM_ACTIVITY_LIST = gql`
  query forumActivities($options: ForumActivityListOptions) {
    forumActivities(options: $options) {
      items {
        ...ForumActivityFragment
      }
      totalItems
    }
  }
  ${FORUM_ACTIVITY_FRAGMENT}
`;

export const FORUM_ACTIVITY_DETAIL = gql`
  query forumActivity($forumActivityId: ID!) {
    forumActivity(forumActivityId: $forumActivityId) {
      ...ForumActivityFragment
    }
  }
  ${FORUM_ACTIVITY_FRAGMENT}
`;

export const UPSERT_FORUM_ACTIVITY = gql`
  mutation upsertForumActivity($input: ForumActivityInput!) {
    upsertForumActivity(input: $input) {
      id
    }
  }
`;

export const DELETE_FORUM_ACTIVITY = gql`
  mutation deleteForumActivity($forumActivityId: ID!) {
    deleteForumActivity(forumActivityId: $forumActivityId) {
      result
      message
    }
  }
`;

export const UPDATE_FORUM_ACTIVITY_STATUS = gql`
  mutation updateForumActivityStatus($forumActivityId: ID!, $status: ForumActivityStatus!) {
    updateForumActivityStatus(forumActivityId: $forumActivityId, status: $status) {
      id
    }
  }
`;

export const FORUM_ACTIVITY_RANK = gql`
  query getHotForumPostsByActivity(
    $options: ForumPostListOptions
    $activityId: ID!
    $forumPostHotDateType: ForumPostHotDateType
  ) {
    getHotForumPostsByActivity(
      options: $options
      activityId: $activityId
      forumPostHotDateType: $forumPostHotDateType
    ) {
      items {
        id
        forumCustomer {
          id
          name
          phone
          headPortrait
        }
        title
        mainImage
        votes
        upVotes
        viewCount
      }
      totalItems
    }
  }
`;

// 帖子相关接口
export const FORUM_POST_LIST = gql`
  query forumPosts($options: ForumPostListOptions, $forumTagId: ID, $forumActivityId: ID) {
    forumPosts(options: $options, forumTagId: $forumTagId, forumActivityId: $forumActivityId) {
      items {
        ...ForumPostFragment
      }
      totalItems
    }
  }
  ${FORUM_POST_FRAGMENT}
`;

export const FORUM_POST_DETAIL = gql`
  query forumPost($forumPostId: ID!) {
    forumPost(forumPostId: $forumPostId) {
      ...ForumPostFragment
    }
  }
  ${FORUM_POST_FRAGMENT}
`;

export const UPSERT_FORUM_POST = gql`
  mutation upsertForumPost($input: ForumPostInput!) {
    upsertForumPost(input: $input) {
      id
    }
  }
`;

export const DELETE_FORUM_POST = gql`
  mutation deleteForumPost($forumPostId: ID!) {
    deleteForumPost(forumPostId: $forumPostId) {
      result
      message
    }
  }
`;

export const AUDIT_FORUM_POST = gql`
  mutation auditForumPost($input: ForumPostAuditInput!) {
    auditForumPost(input: $input) {
      id
    }
  }
`;

export const PUBLISHED_FORUM_POST_LIST = gql`
  query getHotForumPosts($options: ForumPostListOptions, $forumPostHotDateType: ForumPostHotDateType!) {
    getHotForumPosts(options: $options, forumPostHotDateType: $forumPostHotDateType) {
      items {
        ...ForumPostFragment
      }
      totalItems
    }
  }
  ${FORUM_POST_FRAGMENT}
`;

export const TAGHASH_FORUM_POST_LIST = gql`
  query getHotForumPostsByTagHash(
    $options: ForumPostListOptions
    $forumTagHash: String
    $forumPostHotDateType: ForumPostHotDateType
    $isWish: Boolean
  ) {
    getHotForumPostsByTagHash(
      options: $options
      forumTagHash: $forumTagHash
      forumPostHotDateType: $forumPostHotDateType
      isWish: $isWish
    ) {
      items {
        ...ForumPostFragment
      }
      totalItems
    }
  }
  ${FORUM_POST_FRAGMENT}
`;

// 评论相关接口
export const FORUM_REVIEW_LIST = gql`
  query forumReviews($options: ForumReviewListOptions) {
    forumReviews(options: $options) {
      items {
        ...ForumReviewFragment
      }
      totalItems
    }
  }
  ${FORUM_REVIEW_FRAGMENT}
`;

export const FORUM_REVIEW_DETAIL = gql`
  query forumReview($forumReviewId: ID!) {
    forumReview(forumReviewId: $forumReviewId) {
      ...ForumReviewFragment
    }
  }
  ${FORUM_REVIEW_FRAGMENT}
`;

export const AUDIT_FORUM_REVIEW = gql`
  mutation reviewForumAudit($input: ForumReviewAuditInput!) {
    reviewForumAudit(input: $input) {
      ...ForumReviewFragment
    }
  }
  ${FORUM_REVIEW_FRAGMENT}
`;

export const DELETE_FORUM_REVIEW = gql`
  mutation deleteForumReview($forumReviewId: ID!) {
    deleteForumReview(forumReviewId: $forumReviewId) {
      result
      message
    }
  }
`;

// 发文账号相关
export const FORUM_CUSTOMER_LIST = gql`
  query forumCustomers($options: ForumCustomerListOptions) {
    forumCustomers(options: $options) {
      items {
        id
        name
        phone
        headPortrait
        createType
        customerId
      }
      totalItems
    }
  }
`;

export const UPSERT_FORUM_CUSTOMER = gql`
  mutation upsertForumCustomer($input: ForumCustomerInput!) {
    upsertForumCustomer(input: $input) {
      id
    }
  }
`;

export const DELETE_FORUM_CUSTOMER = gql`
  mutation deleteForumCustomer($forumCustomerId: ID!) {
    deleteForumCustomer(forumCustomerId: $forumCustomerId) {
      result
      message
    }
  }
`;

// 帖子置顶
export const FORUM_POST_PIN = gql`
  mutation forumPostPin($forumPostId: ID!, $isPinned: Boolean!) {
    forumPostPin(forumPostId: $forumPostId, isPinned: $isPinned) {
      id
      pinned
    }
  }
`;
