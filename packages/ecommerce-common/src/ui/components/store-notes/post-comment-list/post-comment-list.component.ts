/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy} from '@angular/core';
import {FormBuilder} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {ClrLoadingState} from '@clr/angular';
import {DataService, ModalService, NotificationService} from '@vendure/admin-ui/core';

import {CustomBaseListComponent} from '../../base/custom-base-list.component';
// import {BLIND_BOX_LIST} from '../../blind-box/graphql';
import {DELETE_FORUM_REVIEW, FORUM_POST_LIST, FORUM_REVIEW_LIST} from '../graphql';
import {SortOrder} from '../../../generated-admin-types';
import {PostAuditDialogComponent} from '../post-audit-dialog/post-audit-dialog.component';

import {DateTime} from 'luxon';
import {BehaviorSubject, map, Observable} from 'rxjs';
import {utils, writeFile} from 'xlsx';
import {forumStatusObject} from '../pipes';

type CommentType = any;

@Component({
  selector: 'app-post-comment-list',
  templateUrl: './post-comment-list.component.html',
  styleUrls: ['./post-comment-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostCommentListComponent extends CustomBaseListComponent<any, any> implements OnInit {
  exportLoading$: Observable<ClrLoadingState>;
  exportLoadingSubj = new BehaviorSubject(ClrLoadingState.DEFAULT);

  exportPageSize = 1000;
  postStates = [
    {
      label: '待审核',
      value: 'pending',
    },
    {
      label: '审核通过',
      value: 'pass',
    },
    {
      label: '审核不通过',
      value: 'refuse',
    },
  ];

  FORUM_POST_LIST = FORUM_POST_LIST;

  get curStatus() {
    return this.form?.controls?.status?.getRawValue();
  }

  constructor(
    router: Router,
    route: ActivatedRoute,
    private dataService: DataService,
    private fb: FormBuilder,
    private modal: ModalService,
    private notification: NotificationService,
  ) {
    super(router, route);
    super.setQueryFn(
      () => this.dataService.query(FORUM_REVIEW_LIST),
      res => res.forumReviews,
      (skip, take) => {
        const {title, forumPostId, status} = this.form.getRawValue();
        return {
          options: {
            skip,
            take,
            sort: {createdAt: SortOrder.Desc},
            filter: {
              title: title ? {contains: title} : undefined,
              forumPostId: forumPostId ? {eq: forumPostId} : undefined,
              status: status ? {eq: status} : undefined,
            },
          },
        };
      },
    );
    this.form = this.fb.group({
      title: undefined,
      forumPostId: undefined,
      status: 'pending',
    });
  }

  ngOnInit() {
    this.exportLoading$ = this.exportLoadingSubj.pipe(map(res => res));
    super.ngOnInit();
  }

  selectStatus(s: string) {
    this.form.controls.status.setValue(s);
    this.query();
  }

  reset() {
    this.form.patchValue({
      title: undefined,
      forumPostId: undefined,
    });
    this.query();
  }

  handleAudit(id: string) {
    this.modal
      .fromComponent(PostAuditDialogComponent, {
        locals: {
          id,
          auditHere: true,
          type: 'forumReview',
        },
      })
      .subscribe(res => {
        if (res) {
          this.query();
        }
      });
  }

  deletePost(item: CommentType) {
    this.router.navigate(['./', item.id]).catch(e => {});
  }

  handleClickExport() {
    if (!this.form.controls.forumPostId.getRawValue()) {
      this.modal
        .dialog({
          title: '提示',
          body: `先查询再导出`,
          buttons: [{type: 'primary', label: '确认', returnValue: true}],
        })
        .subscribe(rv => {
          return;
        });
    } else {
      // this.dataService.query(FORUM_REVIEW_LIST)
      this.modal
        .dialog({
          title: '提示',
          body: `导出当前查询帖子标题的帖子评论数据将会花费少许时间`,
          buttons: [
            {type: 'secondary', label: '取消导出', returnValue: false},
            {type: 'primary', label: '确认导出', returnValue: true},
          ],
        })
        .subscribe(res => {
          if (res) {
            this.queryExportData([], 0);
          }
        });
    }
  }

  queryExportData(returnArr: any[], page: number) {
    console.log('查询第' + (page + 1) + '页数据');
    const {forumPostId, status} = this.form.getRawValue();
    this.dataService
      .query<{forumReviews: any}>(FORUM_REVIEW_LIST, {
        options: {
          skip: (page ?? 0) * this.exportPageSize,
          take: this.exportPageSize,
          filter: {
            forumPostId: {
              eq: forumPostId,
            },
            status: status ? {eq: status} : undefined,
          },
        },
      })
      .mapStream(res => res.forumReviews)
      .subscribe(res => {
        if (res) {
          const {totalItems, items} = res;
          const newRv = [...returnArr, ...items];
          const rest = totalItems - (page + 1) * this.exportPageSize;
          if (rest > 0) {
            console.log('存在剩余未查数据');
            this.queryExportData(newRv, ++page);
          } else {
            console.log('已获取全部数据');
            this.handleExportData(newRv);
          }
        }
      });
  }

  handleExportData(res: any[]) {
    console.log(res);
    let title = '';
    const tempData = res.map((i: any) => {
      title = i.forumPost?.title ? i.forumPost.title : title;
      return {
        评论内容: i.content,
        评论用户: i.forumCustomer.name,
        帖子名称: i.forumPost?.title,
        点赞数: i.upVotes,
        评论状态: forumStatusObject[i.status],
      };
    });
    const workSheet = utils.json_to_sheet(tempData);
    const book = utils.book_new();
    utils.book_append_sheet(book, workSheet, '汇总表');
    writeFile(book, title + '_帖子评论导出：' + DateTime.fromJSDate(new Date()).toFormat('yyyy-MM-dd HH:mm') + '.xlsx');

    this.notification.success('导出成功');
  }

  handleClickDelete(id: string) {
    this.modal
      .dialog({
        title: '提示',
        body: `确认删除该评论？`,
        buttons: [
          {type: 'primary', label: '确认', returnValue: true},
          {type: 'secondary', label: '取消', returnValue: false},
        ],
      })
      .subscribe(rv => {
        // return;
        if (rv) {
          this.dataService.mutate(DELETE_FORUM_REVIEW, {forumReviewId: id}).subscribe(res => {
            if (res) {
              this.notification.success('操作成功');
              this.refresh();
            }
          });
        }
      });
  }
}
