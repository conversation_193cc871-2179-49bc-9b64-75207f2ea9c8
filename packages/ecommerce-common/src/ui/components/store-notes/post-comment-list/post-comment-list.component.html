<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left>
      <div class="flex items-center flex-wrap" [formGroup]="form">
        <!-- <label-value label="帖子标题">
          <input type="text" formControlName="title" />
        </label-value> -->
        <label-value label="帖子标题">
          <app-select-async
            [gql]="FORUM_POST_LIST"
            formControlName="forumPostId"
            searchKey="title"
            labelKey="title"
            (change)="query()"
          ></app-select-async>
        </label-value>
        <div class="flex nowrap">
          <button class="btn btn-primary" (click)="query()">查询</button>
          <button class="btn" (click)="reset()">重置</button>
          <button class="btn" (click)="handleClickExport()">导出</button>
        </div>
      </div>
    </vdr-ab-left>
    <vdr-ab-right>
      <!-- <button class="btn btn-primary" [routerLink]="['./create']"><clr-icon shape="plus"></clr-icon>创建</button> -->
    </vdr-ab-right>
  </vdr-action-bar>

  <clr-tabs>
    <clr-tab *ngFor="let item of postStates">
      <button [class]="{active: curStatus === item.value}" (click)="selectStatus(item.value)" clrTabLink>
        {{ item.label }}
      </button>
    </clr-tab>
  </clr-tabs>

  <vdr-data-table
    [items]="items$ | async"
    [currentPage]="currentPage$ | async"
    [itemsPerPage]="itemsPerPage$ | async"
    [totalItems]="totalItems$ | async"
    (pageChange)="setPageNumber($event)"
    (itemsPerPageChange)="setItemsPerPage($event)"
  >
    <vdr-dt-column>评论内容</vdr-dt-column>
    <vdr-dt-column>帖子标题</vdr-dt-column>
    <vdr-dt-column>状态</vdr-dt-column>
    <vdr-dt-column>评论等级</vdr-dt-column>
    <vdr-dt-column>评论时间</vdr-dt-column>
    <vdr-dt-column>操作</vdr-dt-column>
    <ng-template let-item="item">
      <td class="left align-middle">
        <condition-tooltip [content]="item.content" [contentWidth]="'5.5rem'" [contentRow]="2"></condition-tooltip>
      </td>
      <td class="left align-middle">{{ item.forumPost?.title }}</td>
      <td class="left align-middle">{{ item.status | forumStatus }}</td>
      <!-- <td class="left align-middle">
        <div class="flex flex-col items-center">
          <div *ngFor="let tag of item.forumTags">
            {{ tag.name }}
          </div>
        </div>
      </td> -->
      <td class="left align-middle">{{ item.level ? '2级' : '1级' }}</td>
      <td class="left align-middle">{{ item.reviewTime | fmDate }}</td>
      <td class="left align-middle">
        <ng-container *ngIf="item.status === 'pending'">
          <button *vdrIfPermissions="['UpdateForumReview']" class="btn btn-link" (click)="handleAudit(item.id)">
            审核
          </button>
        </ng-container>
        <button class="btn btn-link" [routerLink]="['./', item.id]">查看</button>
        <button class="btn btn-link" (click)="handleClickDelete(item.id)">删除</button>
      </td>
    </ng-template>
  </vdr-data-table>
</vdr-page-block>
