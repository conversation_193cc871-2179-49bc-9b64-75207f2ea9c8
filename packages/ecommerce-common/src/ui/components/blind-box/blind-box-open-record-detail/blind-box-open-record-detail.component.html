<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <button *ngIf="refundable" class="btn" (click)="handleRefund()">主动退款</button>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="order-detail-top">
    <div class="card detail-top">
      <div *ngIf="orderInfo" class="card-header c-header flex justify-between">
        <div>{{ blindboxStatus[orderInfo.status] }}</div>
        <div *ngIf="orderInfo.status === 'assisting'">
          <div *ngIf="restTimeStamp > 0; else assistOvertime">
            剩余可助力倒计时
            <span class="error">{{ (countDown$ | async)?.returnValue }}</span>
          </div>
          <ng-template #assistOvertime><span class="error">助力时间已超时</span></ng-template>
        </div>
        <!-- <button *ngIf="orderInfo?.state === 'PaymentSettled'" class="btn btn-primary" [clrLoading]="isRequesting">执行订单</button> -->
      </div>
    </div>
  </div>

  <div class="clr-row">
    <div class="clr-col-md-8">
      <div *ngIf="wishItemInfo" class="card">
        <div class="card-header c-header">用户的心愿商品</div>
        <div class="card-block p0" style="margin-top: 0px">
          <table class="table">
            <thead>
              <tr>
                <th>商品名称</th>
                <th>价格</th>
                <th>数量</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="align-middle">
                  <div class="flex nowrap items-start">
                    <img
                      class="product-preview"
                      [src]="wishItemInfo.featuredAsset?.preview ?? wishItemInfo.product?.featuredAsset?.preview"
                    />
                    <span>{{ wishItemInfo.name }}</span>
                  </div>
                </td>
                <td class="align-middle">{{ wishItemInfo.price | tokenCNY }}</td>
                <td class="align-middle">1</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div *ngIf="openRecords?.length" class="card">
        <div class="card-header c-header">用户历史开盒商品记录</div>
        <div class="card-block p0 mt0">
          <table class="table" style="margin-top: 0px">
            <thead>
              <tr>
                <th>开盒次数</th>
                <th>商品名称</th>
                <th>商品图片</th>
                <th>商品金额</th>
                <th>商品数量</th>
                <th>开盒时间</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let record of openRecords; index as i">
                <td class="align-middle">第{{ openRecords.length - i }}次</td>
                <td class="align-middle">{{ record.blindBoxItem?.productVariant?.name }}</td>
                <td>
                  <img
                    class="product-preview"
                    [src]="
                      record.blindBoxItem?.productVariant?.featuredAsset?.preview ??
                      record.blindBoxItem?.productVariant?.product?.featuredAsset?.preview
                    "
                  />
                </td>
                <td class="align-middle">{{ record.blindBoxItem?.productVariant?.price | tokenCNY }}</td>
                <td class="align-middle">1</td>
                <td class="align-middle">{{ record.openedAt | fmDate }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="clr-col-md-4">
      <div *ngIf="orderInfo" class="card">
        <div class="card-block">
          <div class="c-header">开盒记录编号</div>
          <div>{{ orderInfo.code }}</div>
          <div class="c-header">支付开盒时间</div>
          <div>{{ orderInfo.paymentAt | fmDate }}</div>
          <div class="c-header">盲盒支付金额</div>
          <div>{{ orderInfo.paymentMetadata?.payerTotal | tokenCNY }}</div>
          <div class="c-header">目前助推力度</div>
          <div>{{ getCurrentAssistProgress(orderInfo) }}</div>
          <div class="c-header">助力人次</div>
          <div>{{ orderInfo.assistCount }}</div>
          <ng-container *ngIf="orderInfo.pickupAt">
            <div class="c-header">提货时间</div>
            <div>{{ orderInfo.pickupAt | fmDate }}</div>
          </ng-container>
          <ng-container *ngIf="orderInfo.blindBoxRefundRecord">
            <div class="c-header">申请退款时间</div>
            <div>{{ orderInfo.blindBoxRefundRecord.refundAt | fmDate }}</div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</vdr-page-block>
