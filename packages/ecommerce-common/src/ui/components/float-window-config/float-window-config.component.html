<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left> 小程序悬浮窗配置 </vdr-ab-left>
    <vdr-ab-right>
      <button
        *vdrIfPermissions="['CreateFloatingWindow', 'UpdateFloatingWindow']"
        type="button"
        [disabled]="detailForm.pristine || detailForm.invalid"
        class="btn btn-primary"
        (click)="save()"
      >
        保存
      </button>
    </vdr-ab-right>
  </vdr-action-bar>
  <div class="card">
    <div class="card-header">基础配置</div>
    <div class="card-block" [formGroup]="detailForm">
      <app-form-field label="是否开启悬浮窗" class="mt3">
        <input type="checkbox" formControlName="isOpen" clrToggle />
      </app-form-field>
      <app-form-field label="悬浮窗背景图片" class="mt3">
        <image-picker formControlName="floatingWindowImage" (select)="markDirty()"></image-picker>
      </app-form-field>
      <app-form-field label="领取后的悬浮窗背景图片" class="mt3">
        <image-picker formControlName="floatingWindowImageReceived" (select)="markDirty()"></image-picker>
      </app-form-field>
      <app-form-field label="图片宽度（单位为像素）" class="mt3">
        <input type="number" formControlName="width" [min]="0" appInteger />
      </app-form-field>
      <app-form-field label="图片高度（单位为像素）" class="mt3">
        <input type="number" formControlName="height" [min]="0" appInteger />
      </app-form-field>
      <app-form-field label="赠送的优惠券" class="mt3">
        <!-- <input type="checkbox" formControlName="enabled" clrToggle /> -->
        <app-select-async [gql]="COUPONS" formControlName="jumpValue" class="w-100"></app-select-async>
      </app-form-field>
      <app-form-field label="领券后是否显示悬浮窗" class="mt3">
        <input type="checkbox" formControlName="isShowAfterReceived" clrToggle />
      </app-form-field>
    </div>
  </div>
</vdr-page-block>
