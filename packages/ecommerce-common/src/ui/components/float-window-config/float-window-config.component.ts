/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef} from '@angular/core';
import {FormGroup, FormBuilder, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {
  BaseDetailComponent,
  DataService,
  LanguageCode,
  NotificationService,
  ServerConfigService,
} from '@vendure/admin-ui/core';
import {COUPONS_WITHOUT_STATISTICS} from '../coupon/graphql';
import {UPSERT_FLOATING_WINDOW} from './graphql';

@Component({
  selector: 'app-float-window-config',
  templateUrl: './float-window-config.component.html',
  styleUrls: ['./float-window-config.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FloatWindowConfigComponent extends BaseDetailComponent<any> implements OnInit {
  detailForm: FormGroup;

  COUPONS = COUPONS_WITHOUT_STATISTICS;

  constructor(
    route: ActivatedRoute,
    router: Router,
    protected dataService: DataService,
    private fb: FormBuilder,
    private notification: NotificationService,
    private ref: ChangeDetectorRef,
    private serverConfig: ServerConfigService,
  ) {
    super(route, router, serverConfig, dataService);
    //
    this.detailForm = this.fb.group({
      id: undefined,
      isOpen: false,
      isShowAfterReceived: false,
      width: undefined,
      height: undefined,
      jumpValue: [undefined, Validators.required],
      floatingWindowImage: [undefined, Validators.required],
      floatingWindowImageReceived: [undefined, Validators.required],
    });
  }

  ngOnInit() {
    this.init();
  }

  protected setFormValues(entity: any, languageCode: LanguageCode): void {
    // throw new Error('Method not implemented.');
    if (!entity?.id) {
      return;
    }
    const {
      id,
      isOpen,
      isShowAfterReceived,
      width,
      height,
      jumpValue,
      floatingWindowImage,
      floatingWindowImageReceived,
    } = entity;
    this.detailForm.patchValue({
      id,
      isOpen,
      isShowAfterReceived,
      width,
      height,
      jumpValue,
      floatingWindowImage,
      floatingWindowImageReceived,
    });
  }

  markDirty() {
    this.ref.detectChanges();
  }

  save() {
    const input = {
      jumpType: 'coupon',
      ...this.detailForm.getRawValue(),
    };
    this.dataService.mutate(UPSERT_FLOATING_WINDOW, {input}).subscribe((res: any) => {
      if (res?.upsertFloatingWindow?.id) {
        this.notification.success('保存成功');
        this.detailForm.markAsPristine();
        this.ref.detectChanges();
      }
    });
  }
}
