import gql from 'graphql-tag';

export const FLOATING_WINDOW = gql`
  query floatingWindow {
    floatingWindow {
      id
      isOpen
      isShowAfterReceived
      width
      height
      floatingWindowImage
      floatingWindowImageReceived
      jumpValue
    }
  }
`;

export const UPSERT_FLOATING_WINDOW = gql`
  mutation upsertFloatingWindow($input: FloatingWindowInput!) {
    upsertFloatingWindow(input: $input) {
      id
    }
  }
`;
