/* eslint-disable @typescript-eslint/no-explicit-any */
import {Injectable} from '@angular/core';
import {Router} from '@angular/router';
import {BaseEntityResolver, DataService} from '@vendure/admin-ui/core';
import {GET_WEAPP_DYNAMIC_ROUTE_CONFIG} from './graphql';

@Injectable({
  providedIn: 'root',
})
export class WeappDynamicRouteConfigResolver extends BaseEntityResolver<any> {
  constructor(dataService: DataService, router: Router) {
    super(
      router,
      {
        id: '',
      },
      id =>
        dataService
          .query<any>(GET_WEAPP_DYNAMIC_ROUTE_CONFIG)
          .refetchOnChannelChange()
          .mapStream(item => {
            const res = item.getWeappDynamicRouteConfig;
            return res;
          }),
    );
  }
}
