<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <button
        *vdrIfPermissions="['CreateWeappDynamicRouteConfig', 'ReadWeappDynamicRouteConfig']"
        class="btn btn-primary"
        (click)="save()"
      >
        保存
      </button>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="card">
    <div class="card-header">导航栏设置</div>
    <div class="card-block">
      <div class="card mt0 mb3" *ngFor="let tabbar of tabbarArr?.controls; let tIdx = index">
        <div class="card-header">导航{{ tIdx + 1 }}</div>
        <div class="card-block" [formGroup]="tabbar">
          <div class="flex">
            <div class="flex-col items-center">
              <image-picker formControlName="iconUrl" [width]="imgSize" [height]="imgSize"></image-picker>
              未选中
            </div>
            <div class="flex-col items-center">
              <image-picker formControlName="selectedIconUrl" [width]="imgSize" [height]="imgSize"></image-picker>
              选中
            </div>
            <div class="flex-col">
              <input type="text" formControlName="text" placeholder="导航栏按钮名称" />
              <app-form-field label="导航栏指向（自定义页面）">
                <app-select-async
                  [gql]="customPageGql"
                  [filter]="tabbarFilter"
                  searchKey="title"
                  labelKey="title"
                  formControlName="routeValue"
                ></app-select-async>
              </app-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="card-header">功能点设置</div>
    <div class="card-block">
      <div class="card mt0 mb3" *ngFor="let et of eventTrackArr?.controls; let eIdx = index">
        <div class="flex justify-between">
          <div class="card-block" [formGroup]="et">
            <app-form-field label="功能页描述">
              <input type="text" formControlName="text" />
            </app-form-field>
            <app-form-field *vdrIfPermissions="['DeleteWeappDynamicRouteConfig']" label="密钥值">
              <input type="text" formControlName="eventScheme" />
            </app-form-field>
            <app-form-field label="功能点指向（自定义页面）">
              <app-select-async
                [gql]="customPageGql"
                [filter]="eventTrackFilter"
                searchKey="title"
                labelKey="title"
                formControlName="routeValue"
              ></app-select-async>
            </app-form-field>
          </div>
          <button
            *vdrIfPermissions="['DeleteWeappDynamicRouteConfig']"
            class="btn-link link-danger"
            (click)="removeEventTrack(eIdx)"
          >
            删除
          </button>
        </div>
      </div>
      <button
        *vdrIfPermissions="['DeleteWeappDynamicRouteConfig']"
        class="btn btn-primary mt2"
        (click)="addEventTrack()"
      >
        <clr-icon shape="plus"></clr-icon>
        添加
      </button>
    </div>
  </div>
</vdr-page-block>
