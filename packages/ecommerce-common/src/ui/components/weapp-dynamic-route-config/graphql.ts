import gql from 'graphql-tag';

export const GET_WEAPP_DYNAMIC_ROUTE_CONFIG = gql`
  query getWeappDynamicRouteConfig {
    getWeappDynamicRouteConfig {
      id
      tabbarConfig {
        iconUrl
        selectedIconUrl
        text
        routeValue
      }
      eventTrackingConfig {
        text
        eventScheme
        routeValue
      }
    }
  }
`;

export const UPSERT_WEAPP_DYNAMIC_ROUTE_CONFIG = gql`
  mutation upsertWeappDynamicRouteConfig($input: WeappDynamicRouteConfigInput!) {
    upsertWeappDynamicRouteConfig(input: $input) {
      id
    }
  }
`;
