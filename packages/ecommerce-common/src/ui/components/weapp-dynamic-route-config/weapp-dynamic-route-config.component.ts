/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy} from '@angular/core';
import {FormArray, FormBuilder, UntypedFormGroup} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {
  BaseDetailComponent,
  DataService,
  LanguageCode,
  NotificationService,
  ServerConfigService,
} from '@vendure/admin-ui/core';
import {CUSTOM_PAGES} from '../../graphql/graphql';
import {UPSERT_WEAPP_DYNAMIC_ROUTE_CONFIG} from './graphql';

@Component({
  selector: 'app-weapp-dynamic-route-config',
  templateUrl: './weapp-dynamic-route-config.component.html',
  styleUrls: ['./weapp-dynamic-route-config.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WeappDynamicRouteConfigComponent extends BaseDetailComponent<any> implements OnInit {
  detailForm: UntypedFormGroup;
  imgSize = '80px';

  customPageGql = CUSTOM_PAGES;
  tabbarFilter = {
    enable: {eq: true},
  };
  eventTrackFilter = {
    enable: {eq: true},
    // type: {eq: 'activePage'},
  };

  get tabbarArr() {
    return this.detailForm?.controls?.tabbarConfig as FormArray;
  }

  get eventTrackArr() {
    return this.detailForm?.controls?.eventTrackingConfig as FormArray;
  }

  constructor(
    private fb: FormBuilder,
    protected dataService: DataService,
    private notification: NotificationService,
    route: ActivatedRoute,
    router: Router,
    serverCfgService: ServerConfigService,
  ) {
    super(route, router, serverCfgService, dataService);
    this.detailForm = this.fb.group({
      id: undefined,
      tabbarConfig: this.fb.array(
        Array.from({length: 3}, () =>
          this.fb.group({iconUrl: undefined, selectedIconUrl: undefined, text: undefined, routeValue: undefined}),
        ),
      ),
      eventTrackingConfig: this.fb.array([]),
    });
  }

  ngOnInit() {
    //
    this.init();
  }

  protected setFormValues(entity: any, languageCode: LanguageCode): void {
    // throw new Error('Method not implemented.');
    if (!entity?.id) {
      return;
    }
    const tabs = entity.tabbarConfig;
    const ets = entity.eventTrackingConfig;
    if (tabs?.length) {
      this.tabbarArr.patchValue(tabs);
    }
    if (ets?.length) {
      ets.forEach((f: any) => {
        const tempGroup = this.fb.group({...f});
        this.eventTrackArr.push(tempGroup);
      });
    }
  }

  addEventTrack() {
    this.eventTrackArr.push(
      this.fb.group({
        text: undefined,
        eventScheme: undefined,
        routeValue: undefined,
      }),
    );
  }

  removeEventTrack(idx: number) {
    this.eventTrackArr.removeAt(idx);
  }

  save() {
    //
    console.log(this.detailForm);

    const input = {
      ...this.detailForm.getRawValue(),
    };
    this.dataService.mutate(UPSERT_WEAPP_DYNAMIC_ROUTE_CONFIG, {input}).subscribe(res => {
      if (res) {
        this.notification.success('保存成功');
      }
    });
  }
}
