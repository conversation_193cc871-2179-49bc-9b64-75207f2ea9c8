import {Component, ChangeDetectionStrategy, Input} from '@angular/core';
import {Dialog} from '@vendure/admin-ui/core';
import {Product, ProductActivities, SelectiveGiftActivity} from '../../../generated-admin-types';
import {Observable, of} from 'rxjs';

type ProductActivityTypes = ProductActivities | SelectiveGiftActivity;

@Component({
  selector: 'app-product-participate-activity-dialog',
  templateUrl: './product-participate-activity-dialog.component.html',
  styleUrls: ['./product-participate-activity-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProductParticipatieActivityDialogComponent implements Dialog {
  @Input() participate: ProductActivityTypes[];
  @Input() title = '提示';
  @Input() prompt: string;
  @Input() comfirmText = '确认';
  @Input() showCancel = false;
  @Input() isMultiProduct = false;
  @Input() multiParticipate: Product[];

  items$: Observable<Array<ProductActivityTypes & {cusTypeName: string; cusPeriod: string}>>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  multiProductItems$: any;

  constructor() {}
  resolveWith: (result?: boolean) => void;

  ngOnInit() {
    if (this.isMultiProduct) {
      this.multiProductItems$ = of(
        this.multiParticipate.map(m => {
          return {
            ...m,
            participatingActivities: (m.participatingActivities as unknown as ProductActivityTypes[]).map(p => {
              return {
                ...p,
                cusPeriod: this.getPeriod(p),
                cusTypeName: this.getActivityType(p),
              };
            }),
          };
        }),
      );
    } else {
      if (this.participate.length) {
        this.items$ = of(
          this.participate.map(m => {
            return {
              ...m,
              cusPeriod: this.getPeriod(m),
              cusTypeName: this.getActivityType(m),
            };
          }),
        );
      }
    }
  }

  queryActivities() {}

  getActivityType(activity: ProductActivityTypes): string {
    const type = activity.__typename;
    switch (type) {
      case 'Coupon':
        return '优惠券';
      case 'DiscountActivity':
        return '第几件几折';
      case 'FullDiscountPresent':
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if ((activity as any).dType === 'amountFullReduction') {
          return '满减满赠-满额';
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } else if ((activity as any).dType === 'quantityFullReduction') {
          return '满减满赠-满件';
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } else if ((activity as any).dType === 'amountFullPresent') {
          return '实付满赠';
        } else {
          return '';
        }
      case 'PurchasePremium':
        return '加价购';
      case 'PackageDiscount':
        return '打包一口价';
      case 'SelectiveGiftActivity':
        return '任选满赠';
      default:
        return '';
    }
  }

  getPeriod(activity: ProductActivityTypes): string {
    const typeName = activity.__typename;
    if (typeName === 'Coupon') {
      return '-';
    } else if (
      typeName === 'DiscountActivity' ||
      typeName === 'FullDiscountPresent' ||
      typeName === 'PackageDiscount' ||
      typeName === 'SelectiveGiftActivity'
    ) {
      const start = new Date(activity.startTime).toLocaleString();
      const end = new Date(activity.endTime).toLocaleString();
      const arr = [start, end];
      return arr.join('至');
    } else if (typeName === 'PurchasePremium') {
      const start = new Date(activity.validityPeriod?.startTime).toLocaleString();
      const end = new Date(activity.validityPeriod?.endTime).toLocaleString();
      const arr = [start, end];
      return arr.join('至');
    } else {
      return '';
    }
  }

  confirm() {
    this.resolveWith(true);
  }

  cancel() {
    this.resolveWith(false);
  }
}
