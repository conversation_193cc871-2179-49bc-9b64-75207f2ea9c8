import gql from 'graphql-tag';
import {PRODUCT, VDR_PRODUCT_VARIANT} from './fragment';
import {ITEM as DISCOUNT_ACTIVITY_FRAGMENT} from '../half-price-list/graphql';
import {PURCHASE_PREMIUM} from '../mark-up-buy-list/graphql';
import {PACKAGE_DISCOUNT_ITEM} from '../package-discount/graphql/graphql';
import {SELECTIVE_GIFT_ACTIVITY_FRAGMENT} from '../optional-gifts/graphql';

export const GET_PRODUCT_DETAIL = gql`
  query GetProductWithVariants($id: ID!, $variantListOptions: ProductVariantListOptions) {
    product(id: $id) {
      ...Product
      productPurchasePermission {
        id
        isMembershipPlanPurchase
        membershipPlans {
          id
          name
          state
        }
        guideMembershipPlan {
          id
          name
          state
        }
      }
      variantList(options: $variantListOptions) {
        items {
          ...ProductVariant
          __typename
        }
        totalItems
        __typename
      }
      __typename
    }
  }
  ${VDR_PRODUCT_VARIANT}
  ${PRODUCT}
`;

export const GET_PRODUCT_VARIANT_IDS = gql`
  query GetProductVariantOptions($id: ID!) {
    product(id: $id) {
      id
      name
      variants {
        id
        enabled
        name
        sku
        options {
          id
          name
          code
          groupId
          __typename
        }
        customFields {
          costPrice
          __typename
        }
        __typename
      }
      __typename
    }
  }
`;

export const CUSTOM_GET_PRODUCT_OPTION_GROUP = gql`
  query GetProductOptionGroup($id: ID!) {
    productOptionGroup(id: $id) {
      ...ProductOptionGroupWithOptions
      __typename
    }
  }

  fragment ProductOptionGroupWithOptions on ProductOptionGroup {
    id
    createdAt
    updatedAt
    languageCode
    code
    name
    translations {
      id
      name
      languageCode
      __typename
    }
    options {
      id
      languageCode
      name
      code
      translations {
        id
        name
        languageCode
        __typename
      }
      __typename
    }
    __typename
  }
`;

/**
 * 活动可用商品
 */
export const GET_ACTIVITY_USABLE_PRODUCTS = gql`
  query activityUsableProducts(
    $promotionType: PromotionType!
    $collectionId: ID
    $startTime: DateTime!
    $endTime: DateTime!
    $isUsable: Boolean
    $options: ProductListOptions
  ) {
    activityUsableProducts(
      promotionType: $promotionType
      collectionId: $collectionId
      startTime: $startTime
      endTime: $endTime
      isUsable: $isUsable
      options: $options
    ) {
      items {
        ...Product
        participatingActivities {
          id
          startsAt
          endsAt
          customFields {
            activityName
          }
        }
      }
      totalItems
    }
  }
  ${PRODUCT}
`;

/**
 * 已添加的活动商品信息查询
 */
export const ADDED_ACTIVITY_PRODUCTS = gql`
  query products($options: ProductListOptions) {
    products(options: $options) {
      items {
        id
        name
        enabled
        variants {
          id
          name
          enabled
          stockOnHand
          stockAllocated
          price
          options {
            id
            name
          }
        }
        customFields {
          subscriptionPlan {
            id
          }
          putOnSaleType
          putOnSaleTime
          timedTakedown
          takedownTime
          unit
          price
        }
      }
      totalItems
    }
  }
`;

export const COUPON = gql`
  fragment ActivityFindCoupon on Coupon {
    id
    createdAt
    updatedAt
    name
    remarks
    type
    claimRestriction
    whetherRestrictUsers
    introduce
    totalQuantity
    customerUnitPrice
    totalOrderAmount
    haveBeenUsedNumber
    residualNumber
    receivedNumber
    state
    enable
    preferentialContent {
      preferentialType
      minimum
      discount
      maximumOffer
      includingDiscountProducts
    }
    validityPeriod {
      type
      startTime
      endTime
      numberOfDays
    }
    applicableProduct {
      applicableType
      productIds
    }
  }
`;

export const FULL_DISCOUNT_PRESENT = gql`
  fragment ActivityFindFullDiscountPresent on FullDiscountPresent {
    id
    name
    displayName
    dType: type
    remarks
    status
    startTime
    endTime
    introduce
    ruleType
    whetherRestrictUsers
    stackingDiscountSwitch
    memberPlanIds
    stackingPromotionTypes
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
      freeGiftValues {
        freeGiftId
        freeGiftName
        freeGiftPrice
        freeGiftProductId
        maximumOffer
      }
      maximumOffer
    }
    applicableProduct {
      applicableType
      productIds
    }
    statisticsData {
      totalPayment
      totalOrders
      customerCount
      averageOrderValue
    }
  }
`;

/**
 * 商品对应的所有活动信息数据
 */
export const PRODUCT_ACTIVITY_FIND_ALL = gql`
  query productActivitiesFindAll($productId: ID!) {
    productActivitiesFindAll(productId: $productId) {
      ... on Coupon {
        ...ActivityFindCoupon
      }
      ... on PurchasePremium {
        ...PurchasePremium
      }
      ... on DiscountActivity {
        ...DiscountActivity
      }
      ... on FullDiscountPresent {
        ...ActivityFindFullDiscountPresent
      }
      ... on PackageDiscount {
        ...PACKAGE_DISCOUNT_ITEM
      }
      ... on SelectiveGiftActivity {
        ...SelectiveGiftActivity
      }
    }
  }
  ${COUPON}
  ${PURCHASE_PREMIUM}
  ${DISCOUNT_ACTIVITY_FRAGMENT}
  ${FULL_DISCOUNT_PRESENT}
  ${PACKAGE_DISCOUNT_ITEM}
  ${SELECTIVE_GIFT_ACTIVITY_FRAGMENT}
`;

export const GET_PRODUCT_SUBSCRIPTION = gql`
  query product($id: ID, $slug: String) {
    product(id: $id, slug: $slug) {
      id
      customFields {
        subscriptionPlan {
          id
          createdAt
          updatedAt
          name
          payUpFront
          fixedStartDate
          autoRenew
          cutOffDays
          subscriptionInterval {
            unit
            frequency
          }
          periodAndDiscount {
            expireNumberOfPeriod
            discountType
            discountAmount
            recommend
          }
        }
      }
    }
  }
`;

export const UPSERT_SUBSCRIPTION = gql`
  mutation upsertSubscriptionPlan($input: UpsertSubscriptionPlanInput, $productId: String!) {
    upsertSubscriptionPlan(input: $input, productId: $productId) {
      id
      updatedAt
    }
  }
`;

export const GET_PRODUCT_OPTION_GROUP_MASTERS = gql`
  query productOptionGroupMasters($productId: ID!) {
    productOptionGroupMasters(productId: $productId) {
      id
      productOptionGroup {
        id
        code
        name
      }
      productOptionMasters {
        id
        productOption {
          id
          code
          name
        }
        asset {
          id
          source
          preview
        }
      }
      asset {
        id
        source
        preview
      }
    }
  }
`;

export const UPSERT_PRODUCT_OPTION_GROUP_MASTERS = gql`
  mutation upsertProductOptionGroupMaster($input: ProductOptionGroupMasterInput!) {
    upsertProductOptionGroupMaster(input: $input) {
      id
      productOptionGroup {
        id
        code
        name
      }
      productOptionMasters {
        id
        productOption {
          id
          code
          name
        }
        asset {
          id
          source
          preview
        }
      }
    }
  }
`;

export const UPSERT_PRODUCT_PURCHASE = gql`
  mutation upsertProductPurchase($input: ProductPurchasePermissionInput!) {
    upsertProductPurchase(input: $input) {
      id
    }
  }
`;

export const BATCH_ENABLE_PRODUCTS = gql`
  mutation batchUpdateProductEnabled($ids: [ID!]!, $enabled: Boolean!) {
    batchUpdateProductEnabled(ids: $ids, enabled: $enabled) {
      id
    }
  }
`;

export const BATCH_UPDATE_PRODUCT_COLLECTION = gql`
  mutation batchUpdateProductCollection($inputs: [UpdateCollectionInput]) {
    batchUpdateProductCollection(inputs: $inputs) {
      id
    }
  }
`;

export const PRODUCTS_SORT_BY_ID = gql`
  query productsSortByProductIds($options: ProductListOptions) {
    productsSortByProductIds(options: $options) {
      items {
        ...Product
      }
      totalItems
    }
  }
  ${PRODUCT}
`;
