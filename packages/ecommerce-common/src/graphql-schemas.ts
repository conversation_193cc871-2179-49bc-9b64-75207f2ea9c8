import {gql} from 'graphql-tag';
import {reviewProductAdminApiExtension} from './review-product.admin.schema';
import {reviewProductShopApiExtension} from './review-product.shop.schema';
// eslint-disable-next-line
const _scalar = gql`
  scalar DateTime
`;

const floatingWindowTypes = gql`
  type FloatingWindow implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    deletedAt: DateTime
    isOpen: Boolean!
    width: Int
    height: Int
    isShowAfterReceived: Boolean!
    floatingWindowImage: String
    floatingWindowImageReceived: String
    jumpType: JumpType!
    jumpValue: String
  }

  extend type Query {
    floatingWindow: FloatingWindow
    getFloatingWindowCouponRecord: Boolean
  }

  extend type Mutation {
    upsertFloatingWindow(input: FloatingWindowInput!): FloatingWindow
  }

  input FloatingWindowInput {
    id: ID
    isOpen: Boolean!
    isShowAfterReceived: Boolean!
    width: Int
    height: Int
    floatingWindowImage: String
    floatingWindowImageReceived: String
    jumpType: JumpType!
    jumpValue: String
  }
`;

const blindBoxTypes = gql`
  enum ShippingFeeType {
    # 正常订单邮费逻辑
    order
    # 盲盒包邮
    blindBoxFreeShipping
    # 盲盒付邮
    blindBoxPayShipping
  }

  type AssistGiftConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxActivity: BlindBoxActivity
    blindBoxActivityId: ID
    isGiftForConvertedUser: Boolean
    isGiftForUnconvertedUser: Boolean
    convertedGift: AssistGift
    convertedGiftId: ID
    nonConvertedGiftLevels: [NonConvertedGiftLevel]
  }

  type NonConvertedGiftLevel {
    convertedGiftId: ID
    level: Int
  }

  type AssistGiftConfigList implements PaginatedList {
    items: [AssistGiftConfig!]!
    totalItems: Int!
  }

  type AssistGiftRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    assistRecord: AssistRecord
    assistRecordId: ID
    assistGift: AssistGift
    assistGiftId: ID
    customer: Customer
    customerId: ID
    isConvertedUser: Boolean
    currentAssistCount: Int
    receivedAt: DateTime
  }

  type AssistGiftRecordList implements PaginatedList {
    items: [AssistGiftRecord!]!
    totalItems: Int!
  }

  type AssistGift implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxActivity: BlindBoxActivity
    blindBoxActivityId: ID
    giftName: String
    type: AssistGiftType
    giftPrice: Int
    giftImage: String
    coupon: Coupon
    targetId: ID
  }

  type AssistGiftList implements PaginatedList {
    items: [AssistGift!]!
    totalItems: Int!
  }

  type AssistRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxBuy: BlindBoxBuy
    blindBoxBuyId: ID
    blindBox: BlindBox
    blindBoxId: ID
    blindBoxOpenRecord: BlindBoxOpenRecord
    blindBoxOpenRecordId: ID
    customer: Customer
    customerId: ID
    assistCustomer: Customer
    assistCustomerId: ID
    assistAt: DateTime
    isSuccess: Boolean
    failureMessage: String
  }

  type AssistRecordList implements PaginatedList {
    items: [AssistRecord!]!
    totalItems: Int!
  }

  type BlindBoxActivityBoxLink implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxActivity: BlindBoxActivity
    blindBoxActivityId: ID
    blindBox: BlindBox
    blindBoxId: ID
    maxAssistLimit: Int
    productProbabilities: [[ProductProbability]]
    assistLimits: [Int]
  }

  type BlindBoxActivityBoxLinkList implements PaginatedList {
    items: [BlindBoxActivityBoxLink!]!
    totalItems: Int!
  }

  type BlindBoxActivityLimitConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    maxBoxPurchaseLimit: Int
    maxAssistLimit: Int
    maxRefundLimit: Int
    assistValidityPeriod: Int
    purchaseLimitPeriod: LimitType
    purchaseLimit: Int
    assistLimitPeriod: LimitType
    assistLimit: Int
    refundLimitPeriod: LimitType
    refundLimit: Int
  }

  type BlindBoxActivityLimitConfigList implements PaginatedList {
    items: [BlindBoxActivityLimitConfig!]!
    totalItems: Int!
  }

  enum BlindBoxActivityStatus {
    # 正常进行中
    normal
    # 失效
    failure
    # 未开始
    notStarted
    # 已结束
    haveEnded
  }

  type BlindBoxActivity implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    remarks: String
    description: String
    startAt: DateTime
    endAt: DateTime
    enabled: Boolean
    statue: BlindBoxActivityStatus
    isFreeShipping: Boolean
    freeShippingThreshold: Int
    price: Int
    baseBlindBoxProbability: Int
    baseBlindBox: BlindBox
    baseBlindBoxId: ID
    blindBoxActivityBoxLinks: [BlindBoxActivityBoxLink]
    assistLimit: Int
    assistPagePoster: ComponentValue
    assistPosterType: ComponentType
    openBoxMode: OpenBoxMode
    stock: Int
    sold: Int
  }

  enum OpenBoxMode {
    # 助力一次开盒
    assistOnce
    # 助力多次开盒
    assistMultiple
  }

  type BlindBoxActivityList implements PaginatedList {
    items: [BlindBoxActivity!]!
    totalItems: Int!
  }

  type BlindBoxBuy implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    code: String
    customer: Customer
    customerId: ID
    price: Int
    wishBlindBoxItem: BlindBoxItem
    blindBoxOpenRecords: [BlindBoxOpenRecord]
    baseBlindBox: BlindBox
    baseBlindBoxId: ID
    wishBlindBox: BlindBox
    wishBlindBoxId: ID
    paymentMetadata: JSON
    status: BlindBoxBuyStatus
    payStatus: BlindBoxBuyPayStatus
    refundReason: String
    paymentAt: DateTime
    assistLimit: Int
    assistCount: Int
    assistExpireAt: DateTime
    blindBoxActivity: BlindBoxActivity
    blindBoxActivityId: ID
    distributor: Distributor
    distributorId: ID
    pickupOpenRecord: BlindBoxOpenRecord
    pickupOpenRecordId: ID
    pickupAt: DateTime
    blindBoxRefundRecord: BlindBoxRefundRecord
    canOpen: Boolean
    maxAssistCount: Int
    openBoxMode: OpenBoxMode
  }

  type BlindBoxRefundRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    customerId: ID
    blindBoxBuy: BlindBoxBuy
    refundReason: String
    blindBoxBuyId: ID
    refundAmount: Int
    refundAt: DateTime
  }

  enum BlindBoxBuyStatus {
    # 待开启
    pendingOpen
    # 已开启
    opened
    # 助力中
    assisting
    # 已提货
    delivered
    # 待提货
    pendingDelivery
    # 已退款
    refunded
  }

  enum BlindBoxBuyPayStatus {
    # 待支付
    pendingPay
    # 已支付
    paid
  }

  type BlindBoxBuyList implements PaginatedList {
    items: [BlindBoxBuy!]!
    totalItems: Int!
  }

  type BlindBoxItem implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBox: BlindBox
    blindBoxId: ID
    productVariant: ProductVariant
    targetId: ID
    type: BlindBoxItemType
    baseProbability: Float
    isWishItem: Boolean
    openBoxImageUrl: String
  }

  type BlindBoxItemList implements PaginatedList {
    items: [BlindBoxItem!]!
    totalItems: Int!
  }

  type BlindBoxOpenRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxBuy: BlindBoxBuy
    blindBoxBuyId: ID
    blindBox: BlindBox
    blindBoxId: ID
    customer: Customer
    customerId: ID
    blindBoxItem: BlindBoxItem
    resultId: ID
    isWishedItem: Boolean
    openedAt: DateTime
    assistCount: Int
    currentAllProbability: JSON
    isFreeShipping: Boolean
    wishBlindBox: BlindBox
  }

  type BlindBoxOpenRecordList implements PaginatedList {
    items: [BlindBoxOpenRecord!]!
    totalItems: Int!
  }

  type BlindBoxOrderRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    blindBoxBuy: BlindBoxBuy
    blindBoxBuyId: ID
    orderLine: OrderLine
    orderLineId: ID
  }

  type BlindBoxOrderRecordList implements PaginatedList {
    items: [BlindBoxOrderRecord!]!
    totalItems: Int!
  }

  type BlindBox implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    wishItemId: ID
    price: Int
    name: String
    description: String
    img: String
    blindBoxItems: [BlindBoxItem]
    wishBlindBoxItem: BlindBoxItem
    remarks: String
  }

  type BlindBoxList implements PaginatedList {
    items: [BlindBox!]!
    totalItems: Int!
  }

  input BlindBoxInput {
    id: ID
    price: Int
    name: String
    description: String
    remarks: String
    img: String
    blindBoxItems: [BlindBoxItemInput!]!
  }

  input BlindBoxItemInput {
    id: ID
    targetId: ID
    type: BlindBoxItemType
    baseProbability: Float
    isWishItem: Boolean
    openBoxImageUrl: String
  }

  enum BlindBoxItemType {
    # 商品
    product
  }

  input BlindBoxListOptions

  input BlindBoxActivityInput {
    id: ID
    name: String
    description: String
    remarks: String
    startAt: DateTime
    endAt: DateTime
    isFreeShipping: Boolean
    freeShippingThreshold: Int
    price: Int
    baseBlindBoxProbability: Int
    baseBlindBoxId: ID
    blindBoxActivityBoxLinks: [BlindBoxActivityBoxLinkInput!]!
    assistLimit: Int
    assistPagePoster: ComponentValueInput
    assistPosterType: ComponentType
    openBoxMode: OpenBoxMode
    stock: Int
  }

  type ProductProbability {
    blindBoxItemId: ID
    targetProbability: Int
  }

  input ProductProbabilityInput {
    blindBoxItemId: ID
    targetProbability: Int
  }

  input BlindBoxActivityBoxLinkInput {
    blindBoxId: ID
    maxAssistLimit: Int
    productProbabilities: [[ProductProbabilityInput]]
    assistLimits: [Int]
  }

  input BlindBoxActivityListOptions

  input BlindBoxActivityLimitConfigInput {
    maxBoxPurchaseLimit: Int
    maxAssistLimit: Int
    maxRefundLimit: Int
    assistValidityPeriod: Int
    purchaseLimitPeriod: LimitType
    purchaseLimit: Int
    assistLimitPeriod: LimitType
    assistLimit: Int
    refundLimitPeriod: LimitType
    refundLimit: Int
  }

  enum AssistGiftType {
    # 优惠券
    coupon
  }

  input AssistGiftInput {
    id: ID
    blindBoxActivityId: ID
    giftName: String
    type: AssistGiftType
    targetId: ID
  }

  input AssistGiftListOptions

  input AssistGiftConfigInput {
    id: ID
    blindBoxActivityId: ID
    isGiftForConvertedUser: Boolean
    isGiftForUnconvertedUser: Boolean
    convertedGiftId: ID
    targetId: ID
    assistGiftType: AssistGiftType
    giftImage: String
    nonConvertedGiftLevels: [NonConvertedGiftLevelInput!]!
  }

  input NonConvertedGiftLevelInput {
    convertedGiftId: ID
    assistGiftType: AssistGiftType
    targetId: ID
    level: Int
    giftImage: String
  }

  input AssistGiftConfigListOptions

  enum WeChatPaymentType {
    wechatProgram
    wechatOfficialAccounts
  }

  type WeChatInitPayInfo {
    isSkipPay: Boolean
    status: Int
    appId: String
    timeStamp: Int
    nonceStr: String
    package: String
    signType: String
    paySign: String
    orderCode: String
  }

  type BlindBoxPreviewProduct {
    wishBlindBox: BlindBox
    baseBlindBox: BlindBox
  }

  input BlindBoxBuyListOptions

  extend type Query {
    blindBoxes(options: BlindBoxListOptions): BlindBoxList!
    blindBox(blindBoxId: ID!): BlindBox

    blindBoxActivities(options: BlindBoxActivityListOptions): BlindBoxActivityList!
    blindBoxActivity(blindBoxActivityId: ID!): BlindBoxActivity

    blindBoxActivityLimitConfig: BlindBoxActivityLimitConfig

    assistGifts(options: AssistGiftListOptions): AssistGiftList!
    assistGift(assistGiftId: ID!): AssistGift

    assistGiftConfigs(options: AssistGiftConfigListOptions): AssistGiftConfigList!
    assistGiftConfig(assistGiftConfigId: ID): AssistGiftConfig

    blindBoxActivityPreviewProduct(blindBoxId: ID!, blindBoxActivityId: ID!): BlindBoxPreviewProduct

    blindBoxUserRemainingCount: CustomerBlindBoxLimit
    # 获取用户剩余可助力次数
    blindBoxUserAssistRemainingCount: CustomerAssistLimit
    wishBlindBoxUserList: [BlindBoxOpenRecord]
    getBlindBoxOpenRecordByOrderCode(orderCode: String!): BlindBoxOpenRecord
    # 根据购买的盲盒订单ID获取购买信息和开盒记录
    getBlindBoxBuyByOrderId(blindBoxBuyId: ID!): BlindBoxBuy
    getBlindBoxBuys(
      options: BlindBoxBuyListOptions
      blindBoxName: String
      distributorName: String
      customerName: String
      customerPhone: String
    ): BlindBoxBuyList
    getBlindBoxBuy(blindBoxBuyId: ID!): BlindBoxBuy
    # 获取可领取的助力礼品
    getAssistGiftRecord: AssistGiftRecord
    # 获取最接近再次开启的盲盒
    getNearestAssistBlindBox: BlindBoxBuy

    # 获取盲盒助力统计数据
    # blindBoxAssistStatistics(startTime: DateTime, endTime: DateTime): BlindBoxAssistStatistics

    # 获取盲盒统计数据
    # blindBoxStatistics(startTime: DateTime, endTime: DateTime): BlindBoxStatistics

    # 每日盲盒购买统计
    # dailyBlindBoxPurchaseStatistics(startTime: DateTime, endTime: DateTime): [DailyBlindBoxBuyStatistics]
    # 心愿盒子选择的人数和次数 成功开到心愿商品的人数/次数
    # wishBlindBoxStatistics(startTime: DateTime, endTime: DateTime): [WishBlindBoxStatistics]

    blindBoxActivityIsBooking(blindBoxActivityId: ID!): Boolean

    customerIsAssist(blindBoxBuyId: ID!): Boolean

    # 盲盒统计
    totalBlindBoxStatistics(startTime: DateTime, endTime: DateTime): BlindBoxTotalStatistics
  }

  type BlindBoxTotalStatistics {
    # 参与助力人数
    assistCustomerCount: Int
    # 参与助力次数
    assistCount: Int
    # 助力领券人数
    assistCouponCustomerCount: Int
    # 领券支付用户数
    couponPayStatistics: Int
    # 领券支付金额
    couponPayAmount: Float
    # 支付转化率
    payConversionRate: Float
    # 助力拉新注册用户数
    assistNewCustomerCount: Int
    # 助力拉新领券用户数
    assistNewCustomerCouponCount: Int
    # 助力拉新领券支付用户数
    assistNewCustomerPayCount: Int
    # 助力注册-支付转化率
    assistNewCustomerPayConversionRate: Float
    # 助力未成交支付用户数
    assistNotPayCustomerFirstPayCount: Int
    # 助力已成交支付用户数
    assistPayCustomerFirstPayCount: Int
    # 盲盒活动访客数
    blindBoxActivityVisit: Int
    # 盲盒活动支付用户数
    blindBoxActivityPayCustomerCount: Int
    # 访问-支付转化率
    blindBoxPayConversionRate: Float
    # 开盒助力分析
    blindBoxActivityStatistics: [BlindBoxActivityStatistics]
    # 领券分析
    couponStatistics: [CouponStatistics]
  }

  type CouponStatistics {
    couponId: ID
    couponName: String
    couponCustomerCount: Int
    couponPayCustomerCount: Int
    payConversionRate: Float
  }

  type BlindBoxActivityStatistics {
    # 盲盒id
    blindBoxId: ID
    # 盲盒信息
    blindBox: BlindBox
    # 购买人数
    purchaseCustomerCount: Int
    # 首次开出心愿商品人数
    openWishBlindBoxCustomerCount: Int
    # 首次未开出心愿商品人数
    openNotWishBlindBoxCustomerCount: Int
    # 分享助力人数
    shareAssistCustomerCount: Int
    # 助力再次开盒人数
    assistOpenBlindBoxCustomerCount: Int
  }

  #type WishBlindBoxStatistics {
  #  # 心愿盲盒ID
  #  wishBlindBoxId: ID
  #  # 心愿盲盒选择人数
  #  purchaseCustomerCount: Int
  #  # 心愿盲盒选择次数
  #  purchaseCount: Int
  #  # 心愿盲盒成功开启人数
  #  openCustomerCount: Int
  #  # 心愿盲盒成功开启次数
  #  openCount: Int
  #}

  #type DailyBlindBoxBuyStatistics {
  #  # 日期
  #  date: String
  #  # 盲盒购买次数
  #  purchaseCount: Int
  #  # 盲盒购买人数
  #  purchaseCustomerCount: Int
  #}

  #type BlindBoxStatistics {
  #  # 盲盒开启人数
  #  openCustomerCount: Int
  #  # 盲盒开启次数
  #  openCount: Int
  #  # 盲盒购买人数
  #  purchaseCustomerCount: Int
  #  # 盲盒购买次数
  #  purchaseCount: Int
  #}

  #type BlindBoxAssistStatistics {
  #  # 助力人数
  #  assistCustomerCount: Int
  #  # 助力次数
  #  assistCount: Int
  #  # 助力拉新人数
  #  assistNewCustomerCount: Int
  #  # 助力拉新成交人数
  #  assistNewCustomerPayCount: Int
  #}

  # 购买限制配置
  type CustomerBlindBoxLimit {
    purchaseLimitPeriod: LimitType
    purchaseLimit: Int
    remainingCount: Int
    limitPeriodCount: Int
  }

  # 助力限制配置
  type CustomerAssistLimit {
    assistLimitPeriod: LimitType
    assistLimit: Int
    remainingCount: Int
    limitPeriodCount: Int
  }

  type AssistStatusResponse {
    isReward: Boolean
    isConvertedUser: Boolean
    assistGiftRecord: AssistGiftRecord
  }

  type ReceiveAssistGiftResponse {
    isReceived: Boolean
    assistGiftRecord: AssistGiftRecord
    gift: UserCoupon
  }

  extend type Mutation {
    upsertBlindBox(input: BlindBoxInput!): BlindBox!
    deleteBlindBox(blindBoxId: ID!): DeletionResponse!

    upsertBlindBoxActivity(input: BlindBoxActivityInput!): BlindBoxActivity!
    deleteBlindBoxActivity(blindBoxActivityId: ID!): DeletionResponse!

    upsertBlindBoxActivityLimitConfig(input: BlindBoxActivityLimitConfigInput!): BlindBoxActivityLimitConfig

    upsertAssistGift(input: AssistGiftInput!): AssistGift!
    deleteAssistGift(assistGiftId: ID!): DeletionResponse!

    upsertAssistGiftConfig(input: AssistGiftConfigInput!): AssistGiftConfig!
    deleteAssistGiftConfig(assistGiftConfigId: ID!): DeletionResponse!

    purchaseBlindBox(blindBoxActivityId: ID!, wishBlindBoxId: ID!, paymentType: WeChatPaymentType): WeChatInitPayInfo!
    # 助力
    assistBlindBox(blindBoxBuyId: ID!): AssistStatusResponse
    # 领取助力礼品
    receiveAssistGift(assistGiftRecordId: ID!): ReceiveAssistGiftResponse

    reOpenBlindBox(blindBoxBuyId: ID!): BlindBoxOpenRecord

    # 盲盒退款
    refundBlindBox(blindBoxBuyId: ID!, refundReason: String): BlindBoxBuy
    # 添加支付到盲盒订单
    testAddPaymentToBlindBoxOrder(orderCode: String!, payment: BlindBoxPaymentInput): Boolean

    openBlindBoxAssist(blindBoxBuyId: ID!): BlindBoxBuy

    takeBlindBoxOrder(blindBoxBuyId: ID!): BlindBoxBuy

    blindBoxActivityBooking(blindBoxActivityId: ID!): Boolean

    addCustomerSource(customerSourceInput: CustomerSourceInput!): CustomerSource
    addCustomerSourceMiniProgram(customerReferralInput: CustomerReferralSourceInput!): CustomerReferralSource

    blindBoxPickUp(blindBoxOpenRecordId: ID!, blindBoxBuyId: ID!): BlindBoxOpenRecord

    failureBlindBoxActivity(blindBoxActivityId: ID!): BlindBoxActivity
  }

  enum CustomerSourceType {
    # 盲盒助力
    blindBoxAssist
    # 盲盒活动
    blindBoxActivity
  }

  type CustomerSource implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customerId: ID
    sourceType: CustomerSourceType
    sourceValue: String
  }

  input CustomerSourceInput {
    sourceType: CustomerSourceType!
    sourceValue: String
  }

  type CustomerReferralSource implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customerId: ID
    sourceType: CustomerReferralSourceType
    sourceValue: String
    additionalInfo: String
    channels: [Channel]
  }

  input CustomerReferralSourceInput {
    sourceType: CustomerReferralSourceType!
    sourceValue: String
    additionalInfo: String
  }

  enum CustomerReferralSourceType {
    wechat
  }

  input BlindBoxPaymentInput {
    method: String!
    metadata: JSON!
  }
`;

const sharedTypes = gql`
  scalar Date
  scalar Time

  enum TemplateType {
    # 优惠券发放
    couponGrants
    # 盲盒活动预约
    blindBoxActivityBooking
  }

  ${blindBoxTypes}

  ${floatingWindowTypes}

  extend type Customer {
    isMember: Boolean
    membershipPlanName: String
    isDistributor: Boolean
    availablePoints: Int
    distributor: Distributor
  }

  enum PageKey {
    # 积分商城首页
    pointsMall
    # 积分签到页
    pointsCheckin
    # 店铺笔记首页
    forumPost
  }

  type ShareSetting implements Node {
    id: ID!
    pageKey: PageKey
    shareTitle: String
    shareImg: String
    channelId: ID
  }

  input ShareSettingInput {
    pageKey: PageKey
    shareTitle: String
    shareImg: String
  }

  enum ExtraState {
    # 售后中
    afterSale
  }

  enum OrderProductType {
    # 普通商品
    normal
    # 优惠券
    coupon
    # 会员卡
    memberCard
  }

  # 订单购买类型
  enum OrderBuyType {
    # 普通购买
    ordinary
    # 积分兑换
    pointsExchange
  }

  # 商品限制组类型
  enum ProductExclusionGroupType {
    # 必须与非限制组合购买
    mustCombineWithNonGroup
    # 可以当前限制组合购买,同时也可以与非限制组合购买,可单独购买
    canCombineWithGroup
    # 不可当前限制组合购买,但可以与非限制组合购买,可单独购买
    noCombineWithGroup
  }

  enum PointsProductType {
    # 普通商品
    normal
    # 优惠券
    coupon
    # 会员卡
    membershipCard
  }

  enum ExchangeConditionType {
    # 统一兑换比例
    unifiedExchangeRatio
    # 自定义兑换比例
    customExchangeRatio
  }

  enum PointsGrantTiming {
    # 确认收货
    confirmReceipt
    # 完成付款
    completePayment
  }

  input PointsProductSkuInput {
    id: ID
    productVariantId: ID!
    enabled: Boolean!
    points: Int
    cash: Int
    exchangeTotal: Int
  }

  input PointsConfigInput {
    validityPeriod: ValidityPeriodInput
    pointsGrantTiming: PointsGrantTiming
    pointsExchangeGrant: Boolean
    pointsRefundGrant: Boolean
    pointsExchangeRate: Int
    pointsRuleText: String
  }

  type PointsConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    validityPeriod: ValidityPeriod
    pointsGrantTiming: PointsGrantTiming
    pointsExchangeGrant: Boolean
    pointsRefundGrant: Boolean
    pointsExchangeRate: Int
    pointsRuleText: String
  }

  input PointsProductInput {
    id: ID
    productId: ID!
    productType: PointsProductType!
    startTime: DateTime!
    endTime: DateTime!
    status: ActivityStatus
    allowPurchaseAtOriginalPrice: Boolean
    exchangeConditionType: ExchangeConditionType
    exchangeLimit: Int
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    pointsProductSkus: [PointsProductSkuInput]!
  }

  type PointsProductSku implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    productVariant: ProductVariant
    productVariantId: ID
    pointsProduct: PointsProduct
    pointsProductId: ID
    enabled: Boolean
    points: Int
    cash: Int
    exchangeTotal: Int
    exchangeCount: Int
    exchangeableCount: Int
  }

  type PointExchange {
    points: Int
    cash: Int
    exchangeTotal: Int
    exchangeCount: Int
  }

  type PointsProduct implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    product: Product
    productId: ID
    productType: PointsProductType
    startTime: DateTime
    endTime: DateTime
    status: ActivityStatus
    allowPurchaseAtOriginalPrice: Boolean
    exchangeConditionType: ExchangeConditionType
    exchangeLimit: Int
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    pointsProductSkus: [PointsProductSku]
    pointExchange: PointExchange
  }

  type PointsProductList implements PaginatedList {
    items: [PointsProduct!]!
    totalItems: Int!
  }

  enum MemberStateInput {
    # 全部
    all
    # 未激活状态
    unactivated
    # 正常状态
    normal
    # 过期状态
    expired
    # 退卡
    returnTheCard
  }

  enum CloseReasonType {
    # 正常关闭
    normal
    # 会员卡退款关闭
    memberCardRefund
  }

  enum GrantType {
    # 自动发放
    automatic
    # 手动领取
    manual
  }

  enum PromotionConditionType {
    # 按金额
    amount
    # 按件数
    quantity
  }

  enum PaymentRewardCouponState {
    # 未领取
    notReceived
    # 已领取
    received
    # 已锁定
    locked
    # 已失效
    invalid
  }

  enum SourceType {
    # 订单购买
    orderBuy
    # 自有商城
    ownMall
    # 自有商城赠送
    ownMallGive
    # 礼品卡
    giftCardOrder
    # 会员卡
    memberOrder
    # 订单
    order
    # 优惠券礼包
    couponBundle
    # 新客福利
    firstCustomerBenefit
    # 签到奖励
    checkinReward
    # 盲盒助力奖励
    blindBoxAssistReward
    #客户等级
    customerLevel
  }

  enum TrackingPageType {
    # 活动页面
    activePage
    # 购物车页面
    shoppingCartPage
    # 个人中心页面
    personalCenterPage
    # 会员卡页面
    memberCardPage
  }

  type ActivityCountdown implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    activityType: PromotionType!
    activityId: ID
    name: String!
    countdownTime: DateTime
    priority: Int
    countdownShowTime: Int
    associatedName: String
    associatedStatus: ActivityStatus
    promotionId: ID
  }

  input ActivityCountdownInput {
    id: ID
    activityType: PromotionType!
    activityId: ID!
    name: String!
    countdownTime: DateTime
    priority: Int
    countdownShowTime: Int
  }

  type ActivityCountdownList implements PaginatedList {
    items: [ActivityCountdown!]!
    totalItems: Int!
  }

  input ActivityCountdownListOptions

  type OrderTracking implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customerId: ID!
    orderId: ID!
    orderAmount: String!
    pageType: TrackingPageType!
    pageId: String
    channelId: ID
  }

  type ActivityContent {
    fullMinus: [String]
    fullDiscount: [String]
    fullPresent: [String]
  }
  enum TrendType {
    # 分时段
    dayParting
    # 累积
    cumulative
  }

  enum TrendSource {
    # 浏览量
    pageViews
    # 访客数
    visitorsCount
    # 支付金额
    totalPayment
    # 支付人数
    paymentCustomerCount
  }

  enum DateTimeType {
    # 年
    year
    # 月
    month
    # 日
    day
  }

  enum RefundableType {
    # 仅退款
    refundOnly
    # 退货退款
    refundAndReturn
    # 主动退款
    voluntaryRefund
  }

  # 商品类型
  enum ProductType {
    # 普通商品
    ordinary
    # 虚拟商品
    virtual
  }

  # 虚拟商品类型
  enum VirtualTargetType {
    # 会员卡
    memberCard
    # 优惠券
    coupon
  }

  # 限购类型
  enum LimitType {
    # 无限制
    unlimited
    # 天
    day
    # 周
    week
    # 月
    month
    # 年
    year
    # 永久
    forever
    # 订单
    order
  }

  enum PurchasePattern {
    # 普通购买
    ordinary
    # 赠品
    gift
    # 加价购
    purchasePremium
  }

  enum ShoppingMoneyGrantTiming {
    # 支付成功
    paymentSuccess
    # 确认收货
    confirmReceipt
  }

  enum SettingKey {
    # 论坛推荐商品组
    forumRecommendProductGroup
    # 个人中心商品组
    personalCenterProductGroup
    # 购物车商品组
    shoppingCartProductGroup
    # 搜索结果商品组
    searchResultProductGroup
    # 会员卡商品组
    memberCardProductGroup
    # 商品详情商品组
    productDetailProductGroup
    # 优惠券商品组
    couponPageProductGroup
    # 分享商品详情页能否返回
    canBackProductDetailPage
    # 是否启用同步会员到有赞
    isEnableYouZanMemberSync
    # 是否启用同步优惠券到有赞
    isEnableYouZanCouponSync
    # 同步会员到有赞最大有效期
    youZanMaxEffectiveTime
    # 会员卡折扣限购来源类型
    memberCardDiscountLimitSourceType
    # 购物金发放时机
    shoppingMoneyGrantTiming
  }

  enum ActivityStatus {
    # 正常进行中
    normal
    # 失效
    failure
    # 未开始
    notStarted
    # 已结束
    haveEnded
  }

  enum FullDiscountPresentType {
    # 金额满减
    amountFullReduction
    # 件数满减
    quantityFullReduction
    # 实付金额满赠
    amountFullPresent
  }

  enum ShoppingCreditConditionType {
    # 金额满减
    amountFullReduction
    # 件数满减
    quantityFullReduction
  }

  enum FreeGiftStatus {
    # 正常进行中
    normal
    # 失效
    failure
  }

  enum PointsSourceType {
    # 订单
    order
    # 售后
    afterSale
    # 有赞同步
    youZanSync
    # 积分兑换
    pointsExchange
    # 积分退回
    pointsRefund
    # 主动退款
    voluntaryRefund
    # 签到奖励
    checkinReward
    # 管理后台操作
    adminManualOperation
  }

  enum CouponType {
    # 满减
    fullSubtraction
    # 折扣
    discount
    # 商品兑换
    exchange
  }

  enum UserCouponState {
    # 未开始
    notStarted
    # 未使用
    unused
    # 过期
    expire
    # 锁定
    lock
    # 已经使用
    haveUsed
  }

  enum CouponState {
    # 正常进行中
    normal
    # 失效
    failure
    # 未开始
    notStarted
    # 已结束
    haveEnded
  }

  enum HotWordType {
    # 搜索热词
    ordinary
    # 精选热词
    choiceness
  }

  enum PreferentialType {
    # 满足条件可用
    satisfy
    # 无门槛
    thresholdFree
  }

  enum ValidityPeriodType {
    # 长期有效
    longTime
    # 有效天数
    validDays
    # 时间段
    temporalInterval
  }

  enum ShareType {
    # 优惠券
    coupon
    # 加价购
    purchasePremium
    # 分销
    distribution
    # 首页
    homePage
    # 商品组页面
    commodityGroupPage
    # 自定义页面
    customPage
    # 会员卡
    memberShipPlan
    # 礼品卡
    giftCard
    # 商品
    product
    # 折扣活动 第X件Y折
    discountActivity
    # 满减活动 满减送
    fullDiscountActivity
    # 打包一口价活动
    packageDiscountActivity
    # 任选增
    selectiveGiftActivity
    # 无参数
    none
    # 搜索分享
    searchResult
    # 优惠券礼包
    couponBundle
    # 会员中心
    memberCenter
    # 会员列表页
    membershipPlanListPage
    # 签到页面
    checkinPage
    # 盲盒活动
    blindBoxActivity
    # 积分商城
    pointsMall
    # 论坛
    forum
    # 论坛话题
    forumTag
    # 论坛笔记/帖子
    forumNote
    # 购物金领取活动
    shoppingCreditsClaim
    # 购物金使用活动
    shoppingCreditsDeduction
  }

  enum RuleType {
    # 阶梯
    ladder
    # 循环
    cycle
  }

  type TrendStatistics {
    toDay: [TrendStatisticsItem]
    yesterday: [TrendStatisticsItem]
  }
  type TrendStatisticsItem {
    statisticsTime: DateTime
    value: Int
  }

  type CustomPageStatistics {
    # 页面ID
    customPageId: ID!
    # 页面标题
    customPageTitle: String!
    # 页面访问量
    pageViews: Int
    # 页面访客数
    visitorsCount: Int
    # 页面点击量
    clickCount: Int
    # 页面点击人数
    clickPeopleCount: Int
    # 点击率
    clickRate: Float
    # 跳失率
    jumpLossRate: Float
    # 下单金额
    orderAmount: Float
    # 下单人数
    orderPeopleCount: Int
    # 下单转化率
    orderConversionRate: Float
    # 支付金额
    paymentAmount: Float
    # 支付人数
    paymentPeopleCount: Int
    # 支付转化率
    paymentConversionRate: Float
  }

  type CustomPageStatisticsList {
    items: [CustomPageStatistics]
    totalItems: Int
  }

  # 销售统计
  type SalesStatistics {
    # 销售额
    salesPrice: Float
    # 退款金额
    refundAmount: Float
    # 营业额
    salesVolume: Float
  }

  # 实时数据统计
  type RealTimeDataStatistics {
    # 待发货订单数
    orderNumberToBeShipped: Int
    # 待处理退款数
    numberOfPendingRefunds: Int
    # 本月销售额
    salesThisMonth: Float
    # 今日支付订单数
    orderNumberPaidToday: Int
    # 咋日支付订单数
    ordersPaidYesterday: Int
    # 订单数比例
    proportionOfOrders: Float
    # 支付订单与咋日相比
    orderNumberPaidCompareYesterday: Float
    # 今日销售额
    salesToday: Float
    # 昨日销售额
    salesYesterday: Float
    # 销售额比例
    proportionOfSales: Float
    # 销售额与昨日相比
    salesCompareYesterday: Float
    # 今日支付人数
    numberOfPayersToday: Int
    # 昨日支付人数
    numberOfPayersYesterday: Int
    # 支付人数比例
    proportionOfPayers: Float
    # 今日访客人数
    visitorsCount: Int
    # 咋日访客人数
    visitorsCountYesterdayNumber: Int
    # 访客人数比例
    proportionOfVisitors: Float
    # 访客人数与咋日相比
    visitorsCountCompareYesterday: Float
  }

  # 数据转化分析
  type ConversionAnalysis {
    # 盲盒访客
    blindBoxVisitor: Int
    # 盲盒浏览量
    blindBoxView: Int
    # 论坛访客
    forumVisitor: Int
    # 论坛浏览量
    forumView: Int
    # 今日访客人数
    visitorsCount: Int
    # 今日浏览量
    pageViews: Int
    # 咋日访客人数
    visitorsCountYesterdayNumber: Int
    # 咋日浏览量
    pageViewsYesterday: Int
    # 访客人数比例
    proportionOfVisitors: Float
    # 访客人数与咋日相比
    visitorsCountCompareYesterday: Float
    # 浏览量比例
    proportionOfPageViews: Float
    # 浏览量与咋日相比
    pageViewsCompareYesterday: Float
    # 今日销售额
    salesToday: Float
    # 昨日销售额
    salesYesterday: Float
    # 销售额比例
    proportionOfSales: Float
    # 销售额与昨日相比
    salesCompareYesterday: Float
    # 今日成功退款金额
    refundAmountToday: Float
    # 昨日成功退款金额
    refundAmountYesterday: Float
    # 退款金额与昨天相比
    refundAmountCompareYesterday: Float
    # 今日营业额
    salesVolumeToday: Float
    # 昨日营业额
    salesVolumeYesterday: Float
    # 营业额与昨天相比
    salesVolumeCompareYesterday: Float
    # 今日客单价
    customerUnitPriceToday: Float
    # 昨日客单价
    customerUnitPriceYesterday: Float
    # 客单价与昨天相比
    customerUnitPriceCompareYesterday: Float
    # 今日支付订单数
    orderNumberPaidToday: Int
    # 咋日支付订单数
    ordersPaidYesterday: Int
    # 订单数比例
    proportionOfOrders: Float
    # 支付订单与咋日相比
    orderNumberPaidCompareYesterday: Float
    # 今日访问-支付转化率
    conversionRateOfVisitsToPaymentsToday: Float
    # 昨日访问-支付转化率
    conversionRateOfVisitsToPaymentsYesterday: Float
    # 访问-支付转化率与昨天相比
    conversionRateOfVisitsToPaymentsCompareYesterday: Float
    # 今日新增发卡数
    newCardNumberToday: Int
    # 昨日新增发卡数
    newCardNumberYesterday: Int
    # 新增发卡数与昨天相比
    newCardNumberCompareYesterday: Float
    # 累积持卡人数
    cumulativeCardholders: Int
    # 昨日持卡人数
    cumulativeCardholdersYesterday: Int
    # 持卡人数与昨天相比
    cumulativeCardholdersCompareYesterday: Float
    # 会员卡支付总人数
    membershipCardPaymentFrequency: Int
    # 会员卡昨日支付总人数
    membershipCardPaymentFrequencyYesterday: Int
    # 会员卡支付总人数与昨天相比
    membershipCardPaymentFrequencyCompareYesterday: Float
    # 会员卡支付总订单数
    membershipCardPaymentOrderNumber: Int
    # 会员卡昨日支付总订单数
    membershipCardPaymentOrderNumberYesterday: Int
    # 会员卡支付总订单数与昨天相比
    membershipCardPaymentOrderNumberCompareYesterday: Float
    # 会员卡支付总金额
    membershipCardPaymentAmount: Float
    # 会员卡昨日支付总金额
    membershipCardPaymentAmountYesterday: Float
    # 会员卡支付总金额与昨天相比
    membershipCardPaymentAmountCompareYesterday: Float
    # 会员卡支付总金额与今日销售额比例
    membershipCardPaymentAmountCompareSales: Float
  }

  type DataStatistics {
    # 销售额
    salesPrice: Float
    # 退款金额
    refundAmount: Float
    # 营业额
    salesVolume: Float
    # 待发货订单数
    orderNumberToBeShipped: Int
    # 待处理退款数
    numberOfPendingRefunds: Int
    # 本月销售额
    salesThisMonth: Float
    # 今日支付订单数
    orderNumberPaidToday: Int
    # 咋日支付订单数
    ordersPaidYesterday: Int
    # 订单数比例
    proportionOfOrders: Float
    # 支付订单与咋日相比
    orderNumberPaidCompareYesterday: Float
    # 今日销售额
    salesToday: Float
    # 昨日销售额
    salesYesterday: Float
    # 销售额比例
    proportionOfSales: Float
    # 销售额与昨日相比
    salesCompareYesterday: Float
    # 今日支付人数
    numberOfPayersToday: Int
    # 昨日支付人数
    numberOfPayersYesterday: Int
    # 支付人数比例
    proportionOfPayers: Float
    # 今日访客人数
    visitorsCount: Int
    # 今日浏览量
    pageViews: Int
    # 咋日访客人数
    visitorsCountYesterdayNumber: Int
    # 咋日浏览量
    pageViewsYesterday: Int
    # 访客人数比例
    proportionOfVisitors: Float
    # 访客人数与咋日相比
    visitorsCountCompareYesterday: Float
    # 浏览量比例
    proportionOfPageViews: Float
    # 浏览量与咋日相比
    pageViewsCompareYesterday: Float
    # 今日成功退款金额
    refundAmountToday: Float
    # 昨日成功退款金额
    refundAmountYesterday: Float
    # 退款金额与昨天相比
    refundAmountCompareYesterday: Float
    # 今日营业额
    salesVolumeToday: Float
    # 昨日营业额
    salesVolumeYesterday: Float
    # 营业额与昨天相比
    salesVolumeCompareYesterday: Float
    # 今日客单价
    customerUnitPriceToday: Float
    # 昨日客单价
    customerUnitPriceYesterday: Float
    # 客单价与昨天相比
    customerUnitPriceCompareYesterday: Float
    # 今日访问-支付转化率
    conversionRateOfVisitsToPaymentsToday: Float
    # 昨日访问-支付转化率
    conversionRateOfVisitsToPaymentsYesterday: Float
    # 访问-支付转化率与昨天相比
    conversionRateOfVisitsToPaymentsCompareYesterday: Float
    # 今日新增发卡数
    newCardNumberToday: Int
    # 昨日新增发卡数
    newCardNumberYesterday: Int
    # 新增发卡数与昨天相比
    newCardNumberCompareYesterday: Float
    # 累积持卡人数
    cumulativeCardholders: Int
    # 昨日持卡人数
    cumulativeCardholdersYesterday: Int
    # 持卡人数与昨天相比
    cumulativeCardholdersCompareYesterday: Float
    # 会员卡支付总人数
    membershipCardPaymentFrequency: Int
    # 会员卡昨日支付总人数
    membershipCardPaymentFrequencyYesterday: Int
    # 会员卡支付总人数与昨天相比
    membershipCardPaymentFrequencyCompareYesterday: Float
    # 会员卡支付总订单数
    membershipCardPaymentOrderNumber: Int
    # 会员卡昨日支付总订单数
    membershipCardPaymentOrderNumberYesterday: Int
    # 会员卡支付总订单数与昨天相比
    membershipCardPaymentOrderNumberCompareYesterday: Float
    # 会员卡支付总金额
    membershipCardPaymentAmount: Float
    # 会员卡昨日支付总金额
    membershipCardPaymentAmountYesterday: Float
    # 会员卡支付总金额与昨天相比
    membershipCardPaymentAmountCompareYesterday: Float
    # 会员卡支付总金额与今日销售额比例
    membershipCardPaymentAmountCompareSales: Float
  }

  type ProductVariantStatistics {
    # SKU ID
    productVariantId: ID
    # 加购人数
    addCartPeopleCount: Int
    # 加购量
    addCartCount: Int
    # 下单人数
    orderPeopleCount: Int
    # 下单件数
    orderCount: Int
    # 支付人数
    payPeopleCount: Int
    # 支付件数
    payCount: Int
    # 支付金额
    payPrice: Float
    # SKU名称
    productVariantName: String
    # 成本价
    costPrice: Float
    # 售价
    price: Float
    # 删除时间
    deletedAt: DateTime
  }

  type ProductStatistics {
    # 产品ID
    productId: Int
    # 产品访客
    visitorsCount: Int
    # 产品浏览量
    pageViews: Int
    # 产品加购人数
    addCartPeopleCount: Int
    # 产品加购量
    addCartCount: Int
    # 产品支付人数
    numberOfPayers: Int
    # 单品转换率
    conversionRateOfSingleProduct: Float
    # 支付件数
    numberOfPaidItems: Int
    # 支付金额
    paymentAmount: Float
    # 申请退款件数
    numberOfRefundItems: Int
    # 成功退款件数
    numberOfSuccessfulRefunds: Int
    # 成功退款金额
    numberOfSuccessfulRefundPrice: Float
    # 成功退款订单数
    numberOfSuccessfulRefundOrders: Int
    # 成功退款人数
    numberOfSuccessfulRefundPeople: Int
  }

  input OperationPlanInput {
    id: ID
    name: String!
    groupCustomerId: ID!
    planType: OperationPlanType!
    planTime: DateTime
    couponId: ID!
    noticeConfig: NoticeConfigInput
  }

  input NoticeConfigInput {
    noticeType: NoticeType!
    noticeContent: NoticeContentInput!
  }

  input NoticeContentInput {
    activityName: String!
    activityContent: String!
    warmPrompt: String!
    jumpType: ShareType!
    jumpValue: String!
  }

  enum GrantTiming {
    # 确认收货
    confirmReceipt
    # 完成付款
    completePayment
  }

  input ShoppingCreditsClaimActivityInput {
    id: ID
    name: String!
    remarks: String
    displayName: String
    startTime: DateTime
    endTime: DateTime
    introduce: String
    ruleType: RuleType!
    type: ShoppingCreditConditionType!
    ruleValues: [RuleValueInput]
    applicableProduct: ApplicableProductInput
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
  }

  type ShoppingCreditsDeductionActivity implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    name: String!
    remarks: String
    displayName: String!
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    introduce: String
    minimum: Int
    deductionRate: Int
    applicableProduct: ApplicableProduct
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    smallProgramQRCodeLink: String
    promotion: Promotion
    promotionId: ID
    activityContent: [String]
    activitySynopsis: String
    activitySuperposition: String
    showLabelInCommodity: Boolean
  }

  type ShoppingCreditsClaimActivity implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    name: String!
    remarks: String
    displayName: String!
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    introduce: String
    ruleType: RuleType
    type: ShoppingCreditConditionType
    ruleValues: [RuleValue!]!
    applicableProduct: ApplicableProduct
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    smallProgramQRCodeLink: String
    promotion: Promotion
    promotionId: ID
    activityContent: [String]
    activitySynopsis: ActivitySynopsis
    activitySuperposition: String
  }

  input ShoppingCreditsDeductionActivityInput {
    id: ID
    name: String!
    remarks: String
    displayName: String
    startTime: DateTime
    endTime: DateTime
    introduce: String
    minimum: Int
    deductionRate: Int
    applicableProduct: ApplicableProductInput
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    showLabelInCommodity: Boolean
  }

  type ShoppingCreditsDeductionActivityList implements PaginatedList {
    items: [ShoppingCreditsDeductionActivity!]!
    totalItems: Int!
  }

  type ShoppingCreditsClaimActivityList implements PaginatedList {
    items: [ShoppingCreditsClaimActivity!]!
    totalItems: Int!
  }

  # generated by generateListOptions function
  input ShoppingCreditsDeductionActivityListOptions

  # generated by generateListOptions function
  input ShoppingCreditsClaimActivityListOptions

  # generated by generateListOptions function
  input SubscriptionMessageRecordListOptions

  type OperationPlanCustomer implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    operationPlan: OperationPlan
    customer: Customer
    isGrant: Boolean
    grantState: Boolean
    isNotice: Boolean
    noticeState: Boolean
    coupon: Coupon
  }

  type OperationPlan implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    groupCustomer: GroupCustomer
    state: OperationPlanState
    planType: OperationPlanType
    planTime: DateTime
    coupon: Coupon
    noticeConfig: NoticeConfig
    operationPlanCustomers: [OperationPlanCustomer]
    numberOfSuccessfulRolls: Int
    numberOfFailedRolls: Int
    numberOfSuccessfulNotice: Int
    numberOfFailedNotice: Int
  }

  type NoticeConfig {
    # 通知方式
    noticeType: NoticeType
    # 通知内容
    noticeContent: NoticeContent
  }

  type NoticeContent {
    # 活动名称
    activityName: String
    # 活动内容
    activityContent: String
    # 温馨提示
    warmPrompt: String
    # 跳转类型
    jumpType: ShareType
    # 跳转值
    jumpValue: String
  }

  enum NoticeType {
    # 短信
    sms
    # 小程序订阅消息
    smallProgram
  }

  enum OperationPlanType {
    # 立即发放
    immediate
    # 定时发放
    scheduled
  }

  enum OperationPlanState {
    # 未开始
    notStarted
    # 进行中
    inProgress
    # 已暂停
    suspended
    # 已结束
    haveEnded
  }

  type OperationPlanCustomerList implements PaginatedList {
    items: [OperationPlanCustomer!]!
    totalItems: Int!
  }

  type GroupCustomerList implements PaginatedList {
    items: [GroupCustomer!]!
    totalItems: Int!
  }

  type OperationPlanList implements PaginatedList {
    items: [OperationPlan!]!
    totalItems: Int!
  }

  type GroupCustomer implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    isBirthday: Boolean
    birthdayMonth: [Int]
    isCardTime: Boolean
    cardStartTime: DateTime
    cardEndTime: DateTime
    customerCount: Int
    operationPlans: [OperationPlan]
    isTradeAmount: Boolean
    tradeAmountMin: Int
    tradeAmountMax: Int
    isMember: Boolean
  }

  input ReturnTheGiftCard {
    giftCardOrderId: ID!
    returnAmount: Int!
    isReturnCoupon: Boolean!
    isReturnMember: Boolean!
  }

  input GroupCustomerInput {
    id: ID
    name: String!
    isBirthday: Boolean!
    birthdayMonth: [Int]
    isCardTime: Boolean!
    cardStartTime: DateTime
    cardEndTime: DateTime
    isTradeAmount: Boolean
    tradeAmountMin: Int
    tradeAmountMax: Int
    isMember: Boolean
  }

  input SettingInput {
    key: SettingKey!
    value: String!
  }

  type Setting implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    key: SettingKey!
    value: String!
  }

  type SettingList implements PaginatedList {
    items: [Setting!]!
    totalItems: Int!
  }

  type ExclusionGroup implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    remarks: String
    type: ProductExclusionGroupType
    exclusionProducts: [ExclusionProduct]
  }

  type ExclusionProduct implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    productId: ID
    exclusionGroupId: ID
    product: Product
    skuIds: [ID]
    exclusionGroup: ExclusionGroup
  }

  type ExclusionGroupList implements PaginatedList {
    items: [ExclusionGroup!]!
    totalItems: Int!
  }

  input ExclusionProductInput {
    productId: ID!
    skuIds: [ID]
  }

  input ExclusionGroupInput {
    id: ID
    name: String!
    remarks: String
    type: ProductExclusionGroupType!
    exclusionProducts: [ExclusionProductInput]
  }

  type RuleValue {
    # 门槛
    minimum: Int!
    # 优惠
    discountValue: DiscountValue
    # 赠品
    freeGiftValues: [FreeGiftValue]
    # 最多可选择赠品数量
    maximumOffer: Int!
  }

  type FreeGiftValue {
    freeGiftId: ID!
    skuId: ID
    freeGiftName: String!
    freeGiftPrice: Int!
    freeGiftProductId: ID!
    maximumOffer: Int!
    priority: Int
  }
  enum DiscountType {
    fixedAmount
    fixedPercent
    noDiscount
  }

  type DiscountValue {
    # 折扣类型
    discountType: DiscountType!
    # 优惠数
    discount: Int!
  }

  type FreeGift implements Node {
    id: ID!
    # 赠品名称
    name: String!
    # 赠品状态
    status: FreeGiftStatus!
    # 赠品商品
    product: Product!
  }

  input FreeGiftInput {
    id: ID
    # 赠品名称
    name: String!
    # 赠品商品
    productId: ID!
  }
  type SelectiveGiftActivity implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    displayName: String!
    name: String!
    type: PromotionConditionType
    remarks: String
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    introduce: String
    ruleType: RuleType
    ruleValues: [RuleValue!]!
    applicableProduct: ApplicableProduct
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    promotion: Promotion
    # start
    activityContent: [String]
    activitySynopsis: String
    activitySuperposition: String
    activityGifts: [Product]
    statisticsData: StatisticsData
    # end
  }

  type FullDiscountPresent implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    displayName: String!
    name: String!
    type: FullDiscountPresentType
    remarks: String
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    introduce: String
    ruleType: RuleType
    ruleValues: [RuleValue!]!
    applicableProduct: ApplicableProduct
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    activityContent: ActivityContent
    activitySynopsis: ActivitySynopsis
    activitySuperposition: String
    promotion: Promotion
    activityGifts: [Product]
    statisticsData: StatisticsData
    showLabelInCommodity: Boolean
  }

  type StatisticsData {
    totalPayment: Int
    totalOrders: Int
    totalDiscountAmount: Int
    customerCount: Int
    averageOrderValue: Int
  }

  type ActivitySynopsis {
    synopsisStr: String
    synopsisTags: [String]
  }

  type OrderPaymentRewardCoupon implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    orderId: ID!
    userCouponId: ID
    couponId: ID!
    paymentRewardActivityId: ID!
    order: Order
    userCoupon: UserCoupon
    coupon: Coupon
    paymentRewardActivity: PaymentRewardActivity
    isClaimed: Boolean
    isUsed: Boolean
  }

  type PaymentRewardActivity implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    deletedAt: DateTime
    name: String
    remarks: String
    status: ActivityStatus
    displayName: String
    startTime: DateTime
    endTime: DateTime
    introduce: String
    applicableProduct: ApplicableProduct
    preferentialType: PreferentialType
    minimum: Int
    grantType: GrantType
    isRecovery: Boolean
    coupons: [Coupon]
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
  }

  type PaymentRewardActivityList implements PaginatedList {
    items: [PaymentRewardActivity!]!
    totalItems: Int!
  }

  input PaymentRewardActivityInput {
    id: ID
    name: String!
    remarks: String
    displayName: String
    startTime: DateTime!
    endTime: DateTime!
    introduce: String
    applicableProduct: ApplicableProductInput!
    preferentialType: PreferentialType!
    minimum: Int
    grantType: GrantType!
    isRecovery: Boolean!
    couponIds: [ID]
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
  }

  input SelectiveGiftActivityInput {
    id: ID
    displayName: String!
    name: String!
    type: PromotionConditionType!
    remarks: String
    status: ActivityStatus
    startTime: DateTime!
    endTime: DateTime!
    introduce: String
    ruleType: RuleType!
    ruleValues: [RuleValueInput!]!
    applicableProduct: ApplicableProductInput!
    stackingDiscountSwitch: Boolean!
    stackingPromotionTypes: [PromotionType]
    whetherRestrictUsers: Boolean!
    memberPlanIds: [ID]
  }

  input FullDiscountPresentInput {
    id: ID
    name: String!
    displayName: String!
    type: FullDiscountPresentType!
    remarks: String
    startTime: DateTime!
    endTime: DateTime!
    introduce: String
    ruleType: RuleType!
    ruleValues: [RuleValueInput!]!
    applicableProduct: ApplicableProductInput!
    stackingDiscountSwitch: Boolean!
    stackingPromotionTypes: [PromotionType]
    whetherRestrictUsers: Boolean!
    groupType: GroupType
    memberPlanIds: [ID]
    showLabelInCommodity: Boolean
  }

  input RuleValueInput {
    minimum: Int!
    discountValue: DiscountValueInput!
    freeGiftValues: [FreeGiftValueInput!]
    maximumOffer: Int!
  }

  input FreeGiftValueInput {
    freeGiftId: ID!
    skuId: ID
    freeGiftName: String!
    freeGiftPrice: Int!
    freeGiftProductId: ID!
    maximumOffer: Int!
    priority: Int
  }
  input DiscountValueInput {
    discountType: DiscountType!
    discount: Int!
  }

  type FullDiscountPresentList implements PaginatedList {
    items: [FullDiscountPresent!]!
    totalItems: Int!
  }

  type SelectiveGiftActivityList implements PaginatedList {
    items: [SelectiveGiftActivity!]!
    totalItems: Int!
  }

  type LocationData {
    value: String
    text: String
    children: [LocationData]
  }

  type ShareData {
    # 分享类型
    shareType: ShareType!
    # 分享数据值
    shareValue: ID!
    # 分销员id
    distributorId: String
    # 是否挑战详情
    isDetail: Boolean
    # 分享路径
    path: String
    # 是否预览
    isPreview: Boolean
  }

  input ProgramLinkInput {
    type: ShareType!
    id: ID!
    path: String
    # 是否预览
    isPreview: Boolean
  }

  type PreferentialContent {
    preferentialType: PreferentialType!
    minimum: Int
    discount: Int
    maximumOffer: Int
    includingDiscountProducts: Boolean
  }

  type ValidityPeriod {
    type: ValidityPeriodType!
    startTime: DateTime
    endTime: DateTime
    numberOfDays: Int
  }

  enum ApplicableType {
    all
    availableGoods
    unusableGoods
  }

  type ApplicableProduct {
    applicableType: ApplicableType!
    productIds: [ID]
  }

  type GoodsForExchange {
    productVariantId: ID!
    price: Int!
  }
  input GoodsForExchangeInput {
    productId: ID!
    price: Int!
    sort: Int
    id: ID
  }
  enum LayoutType {
    oneColumn
    twoColumn
    threeColumn
  }

  input ProductCategoryInput {
    banner: PictureResourceInput
    title: String
    subCategory: [SubCategoryInput]
  }

  input SubCategoryInput {
    banner: PictureResourceInput
    title: String
  }

  type ProductCategory {
    banner: PictureResource
    title: String
    subCategory: [SubCategory]
  }

  type SubCategory {
    banner: PictureResource
    title: String
  }

  type ComponentValue {
    layoutType: LayoutType
    pictures: [PictureResource]
    productGroup: ProductGroup
    productItems: ProductItems
    productCategoryList: [ProductCategory]
    backGroundColor: String
    backGroundImage: PictureResource
    # 字体颜色
    fontColor: String
    # 边框颜色
    borderColor: String
    # 框内颜色
    borderInsideColor: String
    # 倒计时截止时间
    countdownEndTime: DateTime
    # 描述说明
    description: String
    targetId: ID
  }

  enum ProductItemsType {
    oneColumn
    towColumn
  }
  type ProductItems {
    showType: ProductItemsType
    ids: [ID]
  }

  type ProductGroup {
    menuCollection: [MenuCollection]!
    groupType: ProductGroupType!
  }

  type MenuCollection {
    menuName: String!
    collectionId: ID!
  }

  enum ProductGroupType {
    topGroup
    leftGroup
  }

  enum PageType {
    homePage
    commodityGroupPage
    activePage
    membershipPlanPage
    # 签到页
    checkinPage
  }

  enum Direction {
    # 向上
    up
    # 向下
    down
  }

  type PictureResource {
    imgHeight: Int!
    imgWidth: Int!
    imgUrl: String
    videoUrl: String
    alt: String
    jump: [Jump]
  }
  type Jump {
    startAxisX: Int
    startAxisY: Int
    endPointAxisX: Int
    endPointAxisY: Int
    jumpType: JumpType
    jumpValue: String
    jumpName: String
  }

  enum ComponentType {
    banner
    picture
    product
    productItems
    video
    category
    checkin
    # 倒计时
    countdown
    # 购物金
    shoppingCredits
    # 用户等级
    customerLevel
    # 优惠券
    coupon
    # 循环滚动轮播图
    rollingCarousel
  }

  enum JumpType {
    # url
    url
    # 商品详情
    productDetails
    # 不跳转
    blank
    # 活动页
    activePage
    # 优惠券
    coupon
    # 满减活动
    fullDiscount
    # 第X件Y折活动
    discountByQuantity
    # 加价购活动
    purchasePremium
    # 会员卡页面
    memberShipPlan
    # 跳转小程序
    smallProgram
    # 倒计时
    countdown
    # 打包一口价
    packageDiscount
    # 优惠卷礼包
    couponBundle
    # 会员中心
    memberCenter
    # 会员列表页
    membershipPlanListPage
    # 签到页面
    checkinPage
    # 盲盒活动
    blindBoxActivity
    # 积分商城
    pointsMall
    # 论坛
    forum
  }

  enum RedirectType {
    url
    productDetails
    shopHome
    blank
  }

  input CouponInput {
    id: ID
    name: String!
    remarks: String!
    type: CouponType!
    preferentialContent: PreferentialContentInput!
    validityPeriod: ValidityPeriodInput!
    totalQuantity: Int!
    applicableProduct: ApplicableProductInput!
    exchangeApplicableProduct: ApplicableProductInput
    claimRestriction: Int!
    whetherRestrictUsers: Boolean!
    memberPlanIds: [ID]
    introduce: String!
    isShareable: Boolean
    showLabelInCommodity: Boolean
  }

  input ApplicableProductInput {
    applicableType: ApplicableType!
    productIds: [ID]
  }

  input PreferentialContentInput {
    preferentialType: PreferentialType!
    minimum: Int
    discount: Int
    maximumOffer: Int
    includingDiscountProducts: Boolean
  }

  input ValidityPeriodInput {
    type: ValidityPeriodType!
    startTime: DateTime
    endTime: DateTime
    numberOfDays: Int
  }

  enum CouponBundleState {
    # 正常进行中
    normal
    # 失效
    failure
  }
  enum InvalidCondition {
    # 任意一张券失效
    anyOne
    # 所有券失效
    all
  }

  input FirstCustomerBenefitInput {
    id: ID
    name: String!
    remarks: String
    invalidCondition: InvalidCondition!
    introduce: String
    firstCustomerBenefitItems: [FirstCustomerBenefitItemInput]
  }

  input FirstCustomerBenefitItemInput {
    couponId: ID!
    quantity: Int!
  }

  type FirstCustomerBenefit implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    deletedAt: DateTime
    name: String
    remarks: String
    state: CouponBundleState
    invalidCondition: InvalidCondition
    introduce: String
    firstCustomerBenefitItems: [FirstCustomerBenefitItem]
  }

  type FirstCustomerBenefitItem implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    firstCustomerBenefitId: ID!
    couponId: ID!
    coupon: Coupon
    quantity: Int
  }

  type FirstCustomerBenefitList implements PaginatedList {
    items: [FirstCustomerBenefit!]!
    totalItems: Int!
  }

  input CouponBundleInput {
    id: ID
    name: String!
    remarks: String
    totalQuantity: Int
    introduce: String
    invalidCondition: InvalidCondition!
    couponBundleItems: [CouponBundleItemInput]
  }

  input CouponBundleItemInput {
    couponId: ID!
    quantity: Int!
  }

  type CouponBundle implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    name: String!
    remarks: String
    totalQuantity: Int
    receivedQuantity: Int
    state: CouponBundleState
    invalidCondition: InvalidCondition
    introduce: String
    smallProgramQRCodeLink: String
    couponBundleItems: [CouponBundleItem]
  }

  type CouponBundleList implements PaginatedList {
    items: [CouponBundle!]!
    totalItems: Int!
  }

  enum GroupType {
    all
    memberPlan
  }

  type CouponBundleItem implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    couponBundleId: ID!
    couponId: ID!
    coupon: Coupon
    couponBundle: CouponBundle
    quantity: Int
  }

  type Coupon implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    name: String!
    remarks: String
    state: CouponState
    enable: Boolean
    type: CouponType
    showLabelInCommodity: Boolean
    preferentialContent: PreferentialContent
    validityPeriod: ValidityPeriod
    totalQuantity: Int
    applicableProduct: ApplicableProduct
    exchangeApplicableProduct: ApplicableProduct
    claimRestriction: Int
    whetherRestrictUsers: Boolean
    memberPlanIds: [ID]
    introduce: String
    receivedNumber: Float
    residualNumber: Float
    haveBeenUsedNumber: Float
    totalOrderAmount: Float
    customerUnitPrice: Float
    promotion: Promotion
    activityContent: [String]
    activityTime: String
    statisticsData: StatisticsData
    isShareable: Boolean
  }

  type UserCoupon implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    claimAt: DateTime
    useAt: DateTime
    isNew: Boolean
    validFromAt: DateTime
    maturityAt: DateTime
    maturityType: ValidityPeriodType
    coupon: Coupon
    order: Order
    state: UserCouponState
    customer: Customer
  }

  type Announcement implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    value: String
  }

  type HotWord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    type: HotWordType
    name: String
    imgUrl: String
    jumpType: JumpType
    jumpValue: String
    channels: [Channel]
  }

  type WeappDynamicRouteConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    tabbarConfig: [WeappTabbarRouteConfig]
    eventTrackingConfig: [WeappEventTrackingRouteConfig]
    channelId: ID
  }

  input WeappDynamicRouteConfigInput {
    id: ID
    tabbarConfig: [WeappTabbarRouteConfigInput]
    eventTrackingConfig: [WeappEventTrackingRouteConfigInput]
  }

  type WeappTabbarRouteConfig {
    iconUrl: String
    selectedIconUrl: String
    text: String
    routeValue: String
  }

  input WeappTabbarRouteConfigInput {
    iconUrl: String
    selectedIconUrl: String
    text: String
    routeValue: String
  }

  type WeappEventTrackingRouteConfig {
    text: String
    eventScheme: String
    routeValue: String
  }

  input WeappEventTrackingRouteConfigInput {
    text: String
    eventScheme: String
    routeValue: String
  }

  type CustomPage implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    type: PageType!
    title: String!
    showNotice: Boolean!
    showSearch: Boolean!
    showPopup: Boolean
    showCustomerService: Boolean
    showShoppingCart: Boolean
    popup: Popup
    shareTitle: String
    shareImg: String
    shareDescription: String
    enable: Boolean!
    components: [Component!]
    channels: [Channel]
    timingAt: DateTime
    remark: String
    modifiedContent: JSON
  }

  enum PushFrequency {
    # 只推送一次
    once
    # 每次推送
    everyTime
  }

  type Popup implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    title: String
    pictureResource: PictureResource
    deliveryStartTime: Time
    deliveryEndTime: Time
    pushFrequency: PushFrequency
  }

  type Component implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    type: ComponentType!
    data: ComponentValue!
    sort: Int!
    remark: String
    enable: Boolean!
  }
  input ComponentInput {
    id: ID
    type: ComponentType!
    data: ComponentValueInput!
    sort: Int!
    remark: String
    enable: Boolean!
  }

  input ComponentValueInput {
    layoutType: LayoutType
    pictures: [PictureResourceInput]
    productGroup: ProductGroupInput
    productItems: ProductItemsInput
    productCategoryList: [ProductCategoryInput]
    backGroundColor: String
    backGroundImage: PictureResourceInput
    # 字体颜色
    fontColor: String
    # 边框颜色
    borderColor: String
    # 框内颜色
    borderInsideColor: String
    # 倒计时截止时间
    countdownEndTime: DateTime
    # 描述说明
    description: String
    targetId: ID
  }
  input ProductItemsInput {
    showType: ProductItemsType
    ids: [ID]
  }

  input MenuCollectionInput {
    menuName: String!
    collectionId: ID!
  }

  input ProductGroupInput {
    menuCollection: [MenuCollectionInput]!
    groupType: ProductGroupType!
  }

  input PurchasePremiumInput {
    id: ID
    name: String!
    remarks: String
    displayName: String!
    validityPeriod: ValidityPeriodInput!
    minimum: Int!
    applicableProduct: ApplicableProductInput!
    goodsForExchanges: [GoodsForExchangeInput!]!
    stackingDiscountSwitch: Boolean!
    stackingPromotionTypes: [PromotionType]!
    introduce: String!
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
  }

  type PurchasePremium implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    name: String!
    displayName: String!
    remarks: String
    validityPeriod: ValidityPeriod
    state: CouponState
    minimum: Int
    applicableProduct: ApplicableProduct
    purchasePremiumProducts: [PurchasePremiumProduct]
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    introduce: String
    activityContent: [String]
    activitySynopsis: String
    activitySuperposition: String
    promotion: Promotion
    statisticsData: StatisticsData

    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
  }

  input PackageDiscountInput {
    id: ID
    name: String!
    displayName: String!
    remarks: String
    startTime: DateTime!
    endTime: DateTime!
    price: Int!
    selectCount: Int!
    productIds: [ID!]!
    stackingDiscountSwitch: Boolean!
    stackingPromotionTypes: [PromotionType]
    introduce: String
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    showLabelInCommodity: Boolean
  }

  type PackageDiscount implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    introduce: String
    name: String!
    displayName: String!
    remarks: String
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    price: Int
    selectCount: Int
    productIds: [ID]
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    promotion: Promotion
    statisticsData: StatisticsData
    # 活动详情页面的简介
    activitySynopsis: String
    # 活动详情页面规则详情的内容
    activityContent: [String]
    # 活动详情页面规则详情的超叠内容
    activitySuperposition: String
    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    showLabelInCommodity: Boolean
  }

  type PurchasePremiumProduct implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    sort: Int
    price: Int
    enabled: Boolean
    product: Product
  }

  type OrderPromotionResult implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    order: Order
    promResult: PromResult
    isPayReward: Boolean
    isCartShowActuallyPaidGift: Boolean
    isCartShowFullDiscountPresentGift: Boolean
    isCartShowSelectiveGift: Boolean
    isMember: Boolean
  }

  type OrderPromotionResultInput {
    orderId: ID
    promResult: PromResult
  }

  type PromResult {
    orderId: ID
    discountAmount: Int
    promLineResults: [PromLineResult]
    orderLinePromResults: [OrderLinePromResult]
    leftOrderLines: [LeftOrderLine]
    discountByTypes: [DiscountByType]
    gifts: [Gift]
    coupons: [PromCoupon]
    disableMember: Boolean
    disableShoppingCredits: Boolean
    disableCoupon: Boolean
    surcharge: SurchargeResult
    orderTotalPrice: Int
    orderTotalPoints: Int
    shoppingCreditsClaim: Int
    shoppingCreditsDeduction: Int
  }

  type SurchargeResult {
    amount: Int
    details: [SurchargeDetails]
  }

  type SurchargeDetails {
    orderLineId: ID
    amount: Int
  }

  type DiscountByType {
    type: String
    discountAmount: Int
  }

  type LeftOrderLine {
    skuId: ID
    lineId: ID
    count: Int
    price: Int
    discountAmount: Int
  }

  type OrderLinePromResult {
    orderLineId: ID
    skuId: ID
    count: Int
    price: Int
    discountAmount: Int
    discountDetails: [DiscountDetail]
    totalPrice: Int
  }

  type DiscountDetail {
    promInstanceId: ID
    type: String
    superimposeType: SuperimposeType
    superimposeTypes: [String]
    discountCount: Int
    discountAmount: Int
  }
  type PromLineResult {
    promInstanceId: ID
    tags: [String]
    orderLines: [PromOrderLine]
    priority: Int
    type: String
    shouldGroup: Boolean
    description: String
    promTime: String
    promExpireTime: DateTime
    promContent: String
    promOverview: String
    discountType: String
    discountAmount: Int
    discountCount: Int
    meetCondition: Boolean
    superimposeType: SuperimposeType
    superimposeTypes: [String]
    gifts: Gift
    coupons: [PromCoupon]
    lackOfExchangeGoodsCoupons: [PromCoupon]
    totalPoints: Int
    orderLineBlindBoxMap: [OrderLineBlindBoxMap]
    shoppingCredits: Int
  }

  type OrderLineBlindBoxMap {
    orderLineId: ID
    blindBoxOrderBuyId: ID
    blindBoxOrderBuyPrice: Int
    blindBoxOpenRecordId: ID
    blindBoxItemId: ID
  }

  type PromCoupon {
    couponId: ID
    selected: Boolean
    price: Int
    autoSelected: Boolean
    couponType: CouponType
  }

  enum SuperimposeType {
    all
    limit
  }

  type Gift {
    promInstanceId: ID
    giftType: GiftType
    items: [GiftItem]
  }
  enum GiftType {
    free
    mark_up
  }

  type GiftItem {
    giftId: ID
    productId: ID
    count: Int
    skuId: ID
    name: String
    giftPrice: Int
    price: Int
    selected: Boolean
    autoSelected: Boolean
    priority: Int
    isAvailable: Boolean
    minimumStr: String
    ladderPriority: Int
    ladderLevel: Int
  }

  type PromOrderLine {
    skuId: ID
    orderLineId: ID
    discountCount: Int
    discountAmount: Int
    displayInThisGroup: Boolean
    discount: Int
    points: Int
    cash: Int
    claimShoppingCredits: Int
  }

  enum PromotionType {
    # 会员
    member
    # 加价购
    purchaseAtAPremium
    # 优惠券
    coupon
    # 限时折扣
    discountActivity
    # 满减送活动
    fullDiscountPresent
    # 自动促销
    automaticPromotion
    # 打包一口价
    packageDiscount
    # 实付满赠
    actuallyPaid
    # 会员价
    memberPrice
    # 任选满赠
    selectiveGift
    # 订阅
    subscription
    # 支付有礼
    paymentReward
    # 积分兑换
    pointsExchange
    # 盲盒活动
    blindBox
    # 购物金领取活动
    shoppingCreditsClaim
    # 购物金使用活动
    shoppingCreditsDeduction
  }

  input PictureResourceInput {
    imgHeight: Int!
    imgWidth: Int!
    imgUrl: String
    alt: String
    videoUrl: String
    jump: [JumpInput!]!
  }
  input JumpInput {
    startAxisX: Int!
    startAxisY: Int!
    endPointAxisX: Int!
    endPointAxisY: Int!
    jumpType: JumpType!
    jumpValue: String!
    jumpName: String
  }

  input CustomPageInput {
    id: ID
    title: String!
    type: PageType!
    showNotice: Boolean!
    showSearch: Boolean!
    showPopup: Boolean
    showCustomerService: Boolean
    showShoppingCart: Boolean
    popup: PopupInput
    shareTitle: String
    shareImg: String
    shareDescription: String
    enable: Boolean!
    timingAt: DateTime
    components: [ComponentInput!]!
    remark: String
  }

  input PopupInput {
    id: ID
    title: String
    pictureResource: PictureResourceInput
    deliveryStartTime: Time
    deliveryEndTime: Time
    pushFrequency: PushFrequency
  }

  type Banner implements Node {
    id: ID!
    image: String!
    redirect: String!
    redirectType: RedirectType!
  }
  input DistributorInput {
    id: ID
    phone: String!
    name: String
    distributorGroupId: ID!
  }

  input DistributorGroupInput {
    id: ID
    name: String!
  }

  type DistributorOrder implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    distributor: Distributor
    customer: Customer
    distributorGroup: DistributorGroup
    order: Order
  }

  type Distributor implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    phone: String
    customer: Customer
    distributorOrders: [DistributorOrder]
    distributorGroup: DistributorGroup
    orderTotal: Int
    customerCount: Int
    effectiveCustomerNum: Int
    distributorGroupId: ID
    distributorGroupName: String
    effectiveOrderNum: Int
    accumulationAmount: Int
    orderTotalAmount: Int
    detailTime: DateTime
    presentGroupId: ID
  }

  type DistributorGroup implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    orderTotal: Int
    customerCount: Int
    effectiveCustomerNum: Int
    effectiveOrderNum: Int
    accumulationAmount: Int
    detailTime: DateTime
    distributors: [Distributor]
  }

  input MemberPriceInput {
    id: ID
    name: String
    remark: String
  }

  input MemberPriceProductInput {
    productId: ID!
    memberPriceProductVariants: [MemberPriceProductVariantInput!]!
  }

  input MemberPriceProductVariantInput {
    productVariantId: ID!
    memberPriceAmount: Int
    memberDiscount: Int
    discountType: DiscountType
  }

  type MemberPrice implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    name: String
    remark: String
    memberPriceProducts: [MemberPriceProduct]
    membershipPlanStr: String
  }

  type MemberPriceProduct implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    memberPriceProductVariant: [MemberPriceProductVariant]
    product: Product
    memberPrice: MemberPrice
  }

  type MemberPriceProductVariant implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    productVariant: ProductVariant
    memberPriceProduct: MemberPriceProduct
    memberPriceAmount: Int
    memberDiscount: Int
    discountType: DiscountType
  }

  type PersonalCenter implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    topComponentType: ComponentType
    topComponent: ComponentValue
    bottomComponentType: ComponentType
    bottomComponent: ComponentValue
    channelId: ID
  }

  input PersonalCenterInput {
    topComponentType: ComponentType
    topComponent: ComponentValueInput
    bottomComponentType: ComponentType
    bottomComponent: ComponentValueInput
  }

  type DistributorOrderList implements PaginatedList {
    items: [DistributorOrder!]!
    totalItems: Int!
  }

  type MemberPriceList implements PaginatedList {
    items: [MemberPrice!]!
    totalItems: Int!
  }

  type MemberPriceProductList implements PaginatedList {
    items: [MemberPriceProduct!]!
    totalItems: Int!
  }

  type MemberPriceProductVariantList implements PaginatedList {
    items: [MemberPriceProductVariant!]!
    totalItems: Int!
  }

  type PackageDiscountList implements PaginatedList {
    items: [PackageDiscount!]!
    totalItems: Int!
  }

  type FreeGiftList implements PaginatedList {
    items: [FreeGift!]!
    totalItems: Int!
  }

  type DistributorList implements PaginatedList {
    items: [Distributor!]!
    totalItems: Int!
  }

  type DistributorGroupList implements PaginatedList {
    items: [DistributorGroup!]!
    totalItems: Int!
  }

  type PurchasePremiumList implements PaginatedList {
    items: [PurchasePremium!]!
    totalItems: Int!
  }

  type PurchasePremiumProductList implements PaginatedList {
    items: [PurchasePremiumProduct!]!
    totalItems: Int!
  }

  type UserCouponList implements PaginatedList {
    items: [UserCoupon!]!
    totalItems: Int!
  }

  type CouponList implements PaginatedList {
    items: [Coupon!]!
    totalItems: Int!
  }

  type BannerList implements PaginatedList {
    items: [Banner!]!
    totalItems: Int!
  }

  type CustomPageList implements PaginatedList {
    items: [CustomPage!]!
    totalItems: Int!
  }

  type HotWordList implements PaginatedList {
    items: [HotWord!]!
    totalItems: Int!
  }

  type ProductVariantDataStatistics {
    items: [ProductVariantStatistics!]!
    totalItems: Int!
  }

  type AnnouncementList implements PaginatedList {
    items: [Announcement!]!
    totalItems: Int!
  }

  input HotWordInput {
    id: ID
    name: String!
    type: HotWordType!
    imgUrl: String!
    jumpType: JumpType!
    jumpValue: String!
  }
  input AnnouncementInput {
    id: ID
    value: String
  }

  input BannerInput {
    image: String!
    redirect: String!
    redirectType: RedirectType!
  }

  type ApplyDiscount {
    order: Order
    removeDiscounts: [PromotionType]
  }

  type CouponUsability {
    availableCoupons: [UserCoupon]
    couponsAreNotAvailable: [UserCoupon]
    lackOfExchangeGoodsCoupons: [UserCoupon]
  }

  enum SharingType {
    QRCode
    link
    H5
    Scheme
  }

  type OrderDiscounts {
    couponDiscounts: [DiscountsCoupon]
    memberDiscounts: [DiscountsMember]
    purchasePremiumDiscounts: [DiscountsPurchasePremium]
    discountActivities: [DiscountsActivities]
    fullDiscountPresent: [DiscountsFullPresent]
  }
  type DiscountsCoupon {
    discount: Discount
    activities: UserCoupon
  }

  type DiscountsMember {
    discount: Discount
  }

  type DiscountsPurchasePremium {
    discount: Discount
    activities: PurchasePremium
  }

  type DiscountsActivities {
    discount: Discount
    activities: DiscountActivity
  }

  type DiscountsFullPresent {
    discount: Discount
    activities: FullDiscountPresent
  }

  input DistributorSharingInput {
    type: SharingType!
    path: String!
    productId: ID
    shareType: ShareType!
    shareValue: String
    distributorId: ID
  }

  enum PutOnSaleType {
    # 立即上架
    immediate
    # 定时上架
    scheduled
    # 手动上架
    manual
  }

  enum ComplimentaryType {
    # 全部用户
    all
    # 会员用户
    member
  }

  type DistributorBinding implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    distributor: Distributor
    distributorGroup: DistributorGroup
    customer: Customer
  }

  type DistributorBindingList implements PaginatedList {
    items: [DistributorBinding!]!
    totalItems: Int!
  }

  input ComplimentaryCouponObjectInput {
    couponId: ID!
    quantity: Int
    complimentaryType: ComplimentaryType!
    membershipPlanIds: [ID!]
  }

  # 折扣活动
  type DiscountActivity implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    displayName: String!
    name: String!
    remarks: String
    status: ActivityStatus
    startTime: DateTime
    endTime: DateTime
    introduce: String
    minimum: Int
    discount: Int!
    productIds: [ID]
    stackingDiscountSwitch: Boolean
    stackingPromotionTypes: [PromotionType]
    smallProgramQRCodeLink: String
    activityContent: [String]
    activitySynopsis: String
    activitySuperposition: String
    promotion: Promotion
    statisticsData: StatisticsData

    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    showLabelInCommodity: Boolean
  }

  input DiscountActivityInput {
    id: ID
    name: String!
    displayName: String!
    remarks: String
    startTime: DateTime!
    endTime: DateTime!
    introduce: String!
    minimum: Int!
    discount: Int!
    productIds: [ID!]!
    stackingDiscountSwitch: Boolean!
    stackingPromotionTypes: [PromotionType]

    whetherRestrictUsers: Boolean
    groupType: GroupType
    memberPlanIds: [ID]
    showLabelInCommodity: Boolean
  }

  type DiscountActivityList implements PaginatedList {
    items: [DiscountActivity!]!
    totalItems: Int!
  }

  type CouponDataStatistic {
    couponAverage: Int
    totalPayment: Int
    totalOrders: Int
    customerCount: Int
    totalDiscountAmount: Int
    averageOrderValue: Int
    newCustomerCount: Int
    oldCustomerCount: Int
  }

  type Panel {
    accumulative: Int
    addMemberNum: Int
    orderSumTotal: Int
    totalOrder: Int
    customNum: Int
  }

  type CouponHold {
    coupon: Coupon
    usableCoupons: [UserCoupon]
    expiredCoupons: [UserCoupon]
    usedCoupons: [UserCoupon]
    notStartedCoupons: [UserCoupon]
    residualNumber: Int
  }

  type OrderLinesRefundable {
    orderLine: OrderLine
    refundableAmount: Int
    isAllowAfterSale: Boolean
  }

  type CustomerStatistic {
    allCustomer: TransactionCustomerStatistic
    newCustomer: TransactionCustomerStatistic
    oldCustomer: TransactionCustomerStatistic
  }

  type TransactionCustomerStatistic {
    # 成交客户占比
    payCustomerCountRatio: Float
    # 客单价
    customerPrice: Float
    # 访问支付转化率
    conversionRateOfVisitsToPayments: Float
    # 成交客户数
    payCustomerCount: Int
    # 成交订单金额
    payAmount: Float
  }

  type UMengConfig implements Node {
    id: ID!
    appKey: String
    apiSecurity: String
    apiKey: String
    channelId: String
  }

  input UMengConfigInput {
    id: ID
    appKey: String!
    apiSecurity: String!
    apiKey: String!
  }

  type H5LineInfo {
    h5Link: String
    h5QRCode: String
  }

  enum PaymentOrderType {
    order
    membershipOrder
    giftCardOrder
    blindBoxBuyOrder
  }

  type OrderTypeResult {
    orderType: PaymentOrderType
    orderId: ID
  }

  type ProductPurchasePermission implements Node {
    id: ID!
    productId: ID
    isMembershipPlanPurchase: Boolean
    membershipPlans: [MembershipPlan]
    guideMembershipPlanId: ID
    guideMembershipPlan: MembershipPlan
  }

  type MembershipOrder {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    memberOrderUserCoupons: [UserCoupon]
  }

  type PointsHistory implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    sourceId: ID
    sourceType: PointsSourceType
    points: Int
    symbolType: SymbolType
    description: String
  }

  type PointsHistoryList implements PaginatedList {
    items: [PointsHistory!]!
    totalItems: Int!
  }

  enum RestrictionType {
    # 指定地区可购买
    region
    # 指定地区不可购买
    regionExclude
  }

  type RestrictedRegion {
    province: String
    city: String
    district: String
  }

  # 主类型
  type ProductRestrictions implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    isGlobalRestriction: Boolean
    products: [Product]
    restrictedRegions: [RestrictedRegion!]! # 受限区域数组
    restrictionType: RestrictionType! # 枚举类型
  }

  # 定义受限区域输入类型
  input RestrictedRegionInput {
    province: String!
    city: String
    district: String
  }

  # 定义用于创建或更新ProductRestrictions的输入类型
  input ProductRestrictionsInput {
    id: ID
    isGlobalRestriction: Boolean
    productIds: [ID] # 产品 ID 数组
    restrictedRegions: [RestrictedRegionInput!]! # 受限区域数组
    restrictionType: RestrictionType! # 枚举类型
  }

  enum RewardType {
    # 日历
    calendar
    # 周期
    cycle
  }

  enum CheckinType {
    # 日签奖励
    daily
    # 连续签到奖励
    consecutive
  }

  enum PrizeType {
    # 积分
    points
    # 优惠券
    coupon
  }

  type CheckinConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    rewardType: RewardType!
    checkinCycle: CheckinCycle
    content: String
    checkinPrizeDays: [CheckinPrizeDay]
  }

  type CheckinCycle implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    rewardType: RewardType!
    startDate: Date
    endDate: Date
    cycleDuration: Int
    autoGenerateNextCycle: Boolean
    isActive: Boolean
  }

  type CheckinPrizeDay implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    checkinConfig: CheckinConfig
    checkinConfigId: ID
    checkinType: CheckinType!
    checkinPrizes: [CheckinPrize]
    limit: Int
    days: Int
    version: Int
  }

  type CheckinPrize implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    checkinPrizeDay: CheckinPrizeDay
    checkinPrizeDayId: ID
    prizeType: PrizeType!
    coupon: Coupon
    targetId: ID
    quantity: Int
    version: Int
  }

  type CustomerCheckinRecords implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    checkinDate: DateTime
    checkinCycle: CheckinCycle
  }

  type CustomerConsecutiveCheckin implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    consecutiveDays: Int
    checkinCycle: CheckinCycle
    lastCheckinDate: DateTime
  }

  type CustomerPrizeRecords implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    prizeType: PrizeType!
    rewardType: RewardType!
    consecutiveDays: Int
    receivedAt: DateTime
    checkinCycle: CheckinCycle
    checkinPrizeDay: CheckinPrizeDay
    checkinPrizes: [CheckinPrize]
    customerCheckinRecords: CustomerCheckinRecords
  }

  input CheckinConfigInput {
    rewardType: RewardType!
    content: String
    checkinCycle: CheckinCycleInput
    checkinPrizeDays: [CheckinPrizeDayInput]!
  }

  input CheckinCycleInput {
    rewardType: RewardType!
    startDate: Date
    endDate: Date
    cycleDuration: Int
    autoGenerateNextCycle: Boolean
  }

  input CheckinPrizeDayInput {
    checkinType: CheckinType!
    limit: Int
    days: Int
    checkinPrizes: [CheckinPrizeInput]!
  }

  input CheckinPrizeInput {
    prizeType: PrizeType!
    couponId: ID
    quantity: Int
  }

  type CheckinRewardsConfig {
    content: String
    dailyPrizeDay: CheckinPrizeDay
  }

  type ConsecutiveResult {
    consecutiveDays: Int
    isCheckedIn: Boolean
  }
  type CustomerPrizeResult {
    eligiblePrizes: [CheckinPrizeDay]
    receivedPrizeRecords: [CustomerPrizeRecords]
  }

  type ShoppingCreditsConfig implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customPageId: ID
    customPage: CustomPage
    shoppingCreditsRule: String
    channelId: ID
  }

  input ShoppingCreditsConfigInput {
    customPageId: ID
    shoppingCreditsRule: String
  }

  type OrderShoppingCredits {
    orderLineId: ID
    shoppingCreditsClaimAmount: Int
    shoppingCreditsDeductionAmount: Int
  }

  type SubscriptionMessageRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    relatedType: String
    relatedID: String
    isSend: Int
    channelId: ID
  }

  type SubscriptionMessageRecordList implements PaginatedList {
    items: [SubscriptionMessageRecord!]!
    totalItems: Int!
  }

  input CreateSubscriptionMessageRecordInput {
    orderID: String!
  }

  type CreateSubscriptionMessageRecordResult {
    success: Boolean!
    message: String!
  }

  extend type Query {
    checkinRewardsConfig: CheckinRewardsConfig
    checkinConfig: CheckinConfig
    consecutiveDays: ConsecutiveResult
    customerPrizeRecords: CustomerPrizeResult

    customerCheckinRecords(input: CustomerCheckinRecordsInput): [CustomerCheckinRecords]

    pointsHistory(options: PointsHistoryListOptions): PointsHistoryList

    pointsConfig: PointsConfig
    getPointsProducts(isAllowExchange: Boolean, options: PointsProductListOptions): PointsProductList
    pointsProducts(productName: String, options: PointsProductListOptions): PointsProductList
    pointsProduct(id: ID!): PointsProduct

    activeProducts(options: ProductListOptions, promotionType: PromotionType, isExchange: Boolean): ProductList

    exclusionGroups(options: ExclusionGroupListOptions): ExclusionGroupList
    exclusionGroup(id: ID!): ExclusionGroup
    checkSKUIdInOtherExclusionGroup(exclusionProductInputs: [ExclusionProductInput], exclusionGroupId: ID): Boolean

    couponBundle(id: ID!): CouponBundle
    couponBundles(options: CouponBundleListOptions): CouponBundleList

    firstCustomerBenefit(id: ID!): FirstCustomerBenefit
    firstCustomerBenefits(
      startTime: DateTime
      endTime: DateTime
      options: FirstCustomerBenefitListOptions
    ): FirstCustomerBenefitList

    productsSortByProductIds(options: ProductListOptions): ProductList!

    getProductVariants(productId: ID, options: ProductVariantListOptions): ProductVariantList!

    productVariantToMembershipId(membershipId: ID!): ProductVariant
    getRegionsJson(regionNames: [String]!): [LocationData]

    areAllEligibleProductsInactiveOrSoldOut(promotionId: ID!, promotionType: PromotionType!): Boolean

    getOrderTypeByPaymentCode(paymentCode: String!): OrderTypeResult
    desDecode(input: String!): ShareData
    generateH5Link(input: ProgramLinkInput): H5LineInfo

    getUMengConfig: UMengConfig

    generateSmallProgramLink(input: ProgramLinkInput): String
    generateSmallProgramQRCodeLink(input: ProgramLinkInput): String

    banners(options: BannerListOptions): BannerList
    banner(bannerId: String!, options: BannerListOptions): Banner
    customPages(options: CustomPageListOptions): CustomPageList
    customPage(customPageId: ID, options: CustomPageListOptions, isPreview: Boolean): CustomPage
    seriesProducts(collectionId: ID!, options: ProductListOptions): ProductList
    hotWords(options: HotWordListOptions): HotWordList
    hotWord(options: HotWordListOptions, hotWordId: ID!): HotWord
    announcements(options: AnnouncementListOptions): AnnouncementList
    announcement(announcementId: ID!, options: AnnouncementListOptions): Announcement
    getAnnouncement: Announcement
    couponAvailableCount(couponId: ID!): Int

    getAvailableCouponList(customerId: ID!, couponName: String, options: CouponListOptions): CouponList
    coupon(options: CouponListOptions, couponId: ID!): Coupon
    getCouponHold(options: CouponListOptions, couponId: ID!): CouponHold
    getActiveCoupons(orderId: ID): CouponUsability
    userCoupons(
      options: UserCouponListOptions
      customerId: ID
      customerPhone: String
      couponName: String
      couponType: CouponType
    ): UserCouponList
    userCoupon(options: UserCouponListOptions, userCouponId: ID!, removeNewTag: Boolean): UserCoupon

    couponHolder(couponId: ID!, options: UserCouponListOptions): UserCoupon
    couponDataStatistic(couponId: ID!): CouponDataStatistic

    purchasePremiums(options: PurchasePremiumListOptions): PurchasePremiumList
    purchasePremium(purchasePremiumId: ID!, options: PurchasePremiumListOptions): PurchasePremium

    distributorsGroups(
      startDateTime: DateTime
      endDateTime: DateTime
      name: String
      skip: Int
      take: Int
      sortName: String
      sortType: String
      isStatistics: Boolean
      options: DistributorGroupListOptions
    ): DistributorGroupList

    distributorsGroupsByDay(
      startDateTime: DateTime
      endDateTime: DateTime
      name: String
      skip: Int
      take: Int
      sortName: String
      sortType: String
    ): DistributorGroupList

    distributors(
      name: String
      phone: String
      startDateTime: DateTime
      endDateTime: DateTime
      distributorGroupIds: [ID]
      skip: Int
      take: Int
      sortName: String
      sortType: String
    ): DistributorList
    distributorsByDay(
      skip: Int
      take: Int
      name: String
      phone: String
      distributorGroupIds: [ID]
      startDateTime: DateTime
      endDateTime: DateTime
    ): DistributorList
    distributor(options: DistributorListOptions, distributorId: ID!): Distributor

    distributorSharing(input: DistributorSharingInput!): String
    getH5ShareLink(input: DistributorSharingInput!): String

    getOrderDiscounts(orderId: ID): OrderDiscounts

    getDistributor: Distributor

    discountActivities(options: DiscountActivityListOptions): DiscountActivityList
    discountActivity(discountActivityId: ID!, options: DiscountActivityListOptions): DiscountActivity

    fullDiscountPresents(options: FullDiscountPresentListOptions): FullDiscountPresentList
    fullDiscountPresent(id: ID!, options: FullDiscountPresentListOptions): FullDiscountPresent

    selectiveGiftActivities(options: SelectiveGiftActivityListOptions): SelectiveGiftActivityList
    selectiveGiftActivity(id: ID!, options: SelectiveGiftActivityListOptions): SelectiveGiftActivity

    freeGifts(options: FreeGiftListOptions): FreeGiftList
    freeGift(id: ID!, options: FreeGiftListOptions): FreeGift

    productActivitiesFindOne(id: ID!): ProductActivities
    productActivitiesFindAll(productId: ID!): [ProductActivities]

    # 获取可用的商品
    activityUsableProducts(
      promotionType: PromotionType!
      collectionId: ID
      startTime: DateTime!
      endTime: DateTime!
      isUsable: Boolean
      options: ProductListOptions
    ): ProductList

    settings(keyNames: [SettingKey]): [Setting]

    freeGiftByOrder(orderId: ID!): [FreeGift]

    orderPromotionResult(orderIds: [ID!]!): [OrderPromotionResult]
    promotionResultByOrderId(orderId: ID!): OrderPromotionResult

    getRefundableAmount(orderId: ID!, lineIds: [ID], isInclusionShippingPrice: Boolean, isUpdate: Boolean): Int
    getRefundableAmountByOrderId(orderId: ID!): [OrderLinesRefundable]

    markUpByOrder(orderId: ID!): [PurchasePremium]

    merchantVoluntaryRefundByOrder(orderId: ID!): [MerchantVoluntaryRefund]

    dataPanel(membershipPlanId: ID!, startTime: DateTime, endTime: DateTime): Panel
    # 用户人群列表
    groupCustomers(options: GroupCustomerListOptions): GroupCustomerList
    # 用户人群详情
    groupCustomer(id: ID!): GroupCustomer
    # 用户人群统计
    groupCustomerCount(input: GroupCustomerInput): Int

    # 运营计划列表
    operationPlans(options: OperationPlanListOptions): OperationPlanList
    # 运营计划详情
    operationPlan(id: ID!): OperationPlan
    # 运营计划人群列表 id 为运营计划id
    operationPlanCustomers(id: ID!, options: OperationPlanCustomerListOptions): OperationPlanCustomerList

    # 获取订阅消息模板id
    getSubscribeMessageTemplateId: TemplateInfo

    # 获取打包一口价列表
    packageDiscounts(options: PackageDiscountListOptions): PackageDiscountList

    # 获取打包一口价详情
    packageDiscount(id: ID!, options: PackageDiscountListOptions): PackageDiscount

    # 成交客户统计
    transactionCustomerStatistics(startDateTime: DateTime, endDateTime: DateTime): CustomerStatistic

    # 获取matomo站点ID
    getMatomoSiteId: String

    # 获取页面分析数据
    customPageDataStatistics(
      startTime: DateTime
      endTime: DateTime
      take: Int
      skip: Int
      sortName: String
      sortType: String
      pageName: String
    ): CustomPageStatisticsList

    # 获取会员价活动
    memberPrices(options: MemberPriceListOptions): MemberPriceList
    # 获取会员价活动详情
    memberPrice(memberPriceId: ID!, options: MemberPriceListOptions): MemberPrice

    # 获取会员价活动商品
    memberPriceProducts(
      memberPriceId: ID!
      collectionId: ID
      productName: String
      options: MemberPriceProductListOptions
    ): MemberPriceProductList

    # 获取会员价活动商品SKU信息
    memberPriceProductVariants(
      memberPriceProductId: ID!
      options: MemberPriceProductVariantListOptions
    ): MemberPriceProductVariantList

    # 获取订单是否有会员价活动
    getOrderIsIncludeMemberPrice(orderId: ID!): Boolean

    # 获取礼品卡购卡记录
    giftCardOrders(
      giftCardId: ID
      phone: String
      distributorId: ID
      options: GiftCardOrderListOptions
    ): GiftCardOrderList

    # 获取礼品卡退卡记录
    giftCardReturnOrders(
      options: GiftCardReturnListOptions
      giftCardId: ID
      giftCardOrderCode: String
      phone: String
      distributorId: ID
    ): GiftCardReturnList

    # 获取支付有礼活动列表
    paymentRewardActivities(options: PaymentRewardActivityListOptions): PaymentRewardActivityList
    # 获取支付有礼活动详情
    paymentRewardActivity(id: ID!, options: PaymentRewardActivityListOptions): PaymentRewardActivity

    # 获取分享设置
    shareSettings(pageKeys: [PageKey]): [ShareSetting]

    getShoppingCreditsConfig: ShoppingCreditsConfig

    shoppingCreditsClaimActivity(
      shoppingCreditsClaimActivityId: ID!
      options: ShoppingCreditsClaimActivityListOptions
    ): ShoppingCreditsClaimActivity

    shoppingCreditsClaimActivities(options: ShoppingCreditsClaimActivityListOptions): ShoppingCreditsClaimActivityList

    shoppingCreditsDeductionActivities(
      options: ShoppingCreditsDeductionActivityListOptions
    ): ShoppingCreditsDeductionActivityList
    shoppingCreditsDeductionActivity(
      shoppingCreditsDeductionActivityId: ID!
      options: ShoppingCreditsDeductionActivityListOptions
    ): ShoppingCreditsDeductionActivity

    shoppingCreditsClaimAndDeductionAmount(orderId: ID!): [OrderShoppingCredits]

    # 订阅消息记录查询
    subscriptionMessageRecords(options: SubscriptionMessageRecordListOptions): SubscriptionMessageRecordList
    subscriptionMessageRecord(id: ID!): SubscriptionMessageRecord

    # 获取个人中心组件设置
    personalCenter: PersonalCenter

    activityCountdowns(
      options: ActivityCountdownListOptions
      activityType: PromotionType
      activityStatus: ActivityStatus
      activityName: String
    ): ActivityCountdownList
    activityCountdown(activityCountdownId: ID!): ActivityCountdown

    activityCountdownByProduct(productId: ID!): ActivityCountdown
    activityCountdownByCart: CartActivityCountdown

    # 获取小程序动态导出配置
    getWeappDynamicRouteConfig: WeappDynamicRouteConfig
  }

  type CartActivityCountdown {
    activityCountdown: ActivityCountdown
    activityTag: String
  }

  type GiftCardReturnList implements PaginatedList {
    items: [GiftCardReturn!]!
    totalItems: Int!
  }

  type GiftCardReturn implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
  }

  type GiftCardOrder implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    code: String
    giftCardDistributor: Distributor
    giftCardUserCoupons: [UserCoupon]
  }

  # generated by generateListOptions function
  input GiftCardOrderListOptions

  type GiftCardOrderList implements PaginatedList {
    items: [GiftCardOrder!]!
    totalItems: Int!
  }
  type TemplateInfo {
    couponGrants: [String]
    blindBoxActivityBooking: [String]
  }

  input ProductPurchasePermissionInput {
    productId: ID!
    isMembershipPlanPurchase: Boolean!
    guideMembershipPlanId: ID
    membershipPlanIds: [ID]!
  }

  type ErrorLogs implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    activeUserId: ID
    errorType: String
    errorDetail: JSON
  }

  type CustomerCheckinResult {
    success: Boolean
    message: String
    totalPoints: Int
    couponCount: Int
    couponPracticalCount: Int
  }

  input CustomerCheckinRecordsInput {
    year: Int
    month: Int
  }

  extend type Mutation {
    customerCheckin: CustomerCheckinResult

    upsertCheckinConfig(input: CheckinConfigInput!): CheckinConfig

    upsertProductRestrictions(input: ProductRestrictionsInput!): ProductRestrictions

    upsertPointsConfig(input: PointsConfigInput!): PointsConfig

    upsertPointsProduct(input: PointsProductInput!): PointsProduct
    softDeletePointsProduct(id: ID!): DeletionResponse

    upsertCouponBundle(input: CouponBundleInput!): CouponBundle
    deleteCouponBundle(id: ID!): DeletionResponse
    failureCouponBundle(id: ID!): CouponBundle

    upsertFirstCustomerBenefit(input: FirstCustomerBenefitInput!): FirstCustomerBenefit
    deleteFirstCustomerBenefit(id: ID!): DeletionResponse
    failureFirstCustomerBenefit(id: ID!): FirstCustomerBenefit

    upsertProductPurchase(input: ProductPurchasePermissionInput!): ProductPurchasePermission

    reportedDistributorRecord(md5Str: String, sourceCode: String, distributionId: String): Boolean
    createErrorLog(errorType: String!, errorStatus: String!, details: JSON!): ErrorLogs

    updateAssetSize(pageSize: Int, page: Int, isUpdateAll: Boolean): String
    distributorDetailStatistics: String
    distributorOrderTotalAmountStatistics: String
    # 添加或修改分销员组
    upsertDistributorGroup(input: DistributorGroupInput): DistributorGroup
    bindingOrderLineTracking(
      orderId: ID!
      productVariantId: ID!
      trackingPageType: TrackingPageType!
      trackingPageId: ID
    ): String
    addOrderTracking(orderId: ID!, trackingPageType: TrackingPageType!, trackingPageId: ID): OrderTracking
    upsertUMengConfig(input: UMengConfigInput): UMengConfig
    createBanner(input: BannerInput!): Banner
    updateBanner(input: BannerInput!, bannerId: String!): Banner
    deleteBanner(bannerId: String!): ID
    upsertCustomPage(input: CustomPageInput): CustomPage
    updateSortCustomPage(customPageId: ID!, componentId: ID!, direction: Direction!): CustomPage
    transitionEnableCustomPage(enable: Boolean!, customPageId: ID!): CustomPage
    deleteCustomPage(customPageId: ID!): DeletionResponse
    upsertHotWord(input: HotWordInput!): HotWord
    deleteHotWord(hotWordId: ID!): DeletionResponse
    upsertAnnouncement(input: AnnouncementInput!): Announcement

    upsertCoupon(input: CouponInput!): Coupon
    failureUserCoupon(userCouponId: ID!): UserCoupon
    failureCoupon(couponId: ID): Coupon
    softDeleteCoupon(couponId: ID!): DeletionResponse
    enableSwitchCoupon(couponId: ID!): Coupon
    claimCoupon(couponId: ID!): UserCoupon
    complimentaryCouponToMember(input: ComplimentaryCouponObjectInput!): Coupon
    grantCoupon(quantity: Int!, couponId: ID!, customerId: ID!): Coupon

    applyCoupon(userCouponId: ID!, orderId: ID): Order
    orderApplyCoupon(orderId: ID!): Order
    cancelCoupon(orderId: ID): Order
    applyMember(orderId: ID): Order
    cancelMember(orderId: ID): Order
    applyShoppingCredits(orderId: ID!): Order
    cancelShoppingCredits(orderId: ID!): Order
    selectGift(orderId: ID!, skuId: ID!, giftId: ID!, promInstanceId: ID!, ladderLevel: Int): Order
    cancelGift(orderId: ID!, giftId: ID!, promInstanceId: ID!): Order

    upsertPurchasePremium(input: PurchasePremiumInput!): PurchasePremium
    purchasePremiumFailure(purchasePremiumId: ID!): PurchasePremium
    softDeletePurchasePremium(purchasePremiumId: ID!): DeletionResponse

    selectMarkUp(orderId: ID!, skuId: ID!, productId: ID!, promInstanceId: ID!): Order
    cancelMarkUp(orderId: ID!, productId: ID!, promInstanceId: ID!): Order
    cancelAllMarkUp(orderId: ID!): Order

    upsertDistributor(input: DistributorInput): Distributor

    boundDistributor(distributorId: ID!): String
    softDeleteDistributor(distributorId: ID!): DeletionResponse

    upsertDiscountActivity(input: DiscountActivityInput!): DiscountActivity
    failureDiscountActivity(discountActivityId: ID!): DiscountActivity
    softDeleteDiscountActivity(discountActivityId: ID!): DeletionResponse

    upsertFullDiscountPresent(input: FullDiscountPresentInput!): FullDiscountPresent
    softDeleteFullDiscountPresent(id: ID!): DeletionResponse
    failureFullDiscountPresent(id: ID!): FullDiscountPresent

    upsertSelectiveGiftActivity(input: SelectiveGiftActivityInput!): SelectiveGiftActivity
    softDeleteSelectiveGiftActivity(id: ID!): DeletionResponse
    failureSelectiveGiftActivity(id: ID!): SelectiveGiftActivity

    upsertFreeGift(input: FreeGiftInput!): FreeGift
    failureFreeGift(id: ID!): FreeGift
    softDeleteFreeGift(id: ID!): DeletionResponse

    upsertSetting(input: [SettingInput]!): [Setting]

    automaticPromotionCreate: Promotion

    # 用户点击小程序
    userClick: Boolean
    # 用户点击商品进入详情页
    userClickProduct(productId: ID): Boolean

    # 新建修改用户人群
    upsertGroupCustomer(input: GroupCustomerInput!): GroupCustomer
    # 删除用户人群
    deleteGroupCustomer(id: ID!): DeletionResponse

    # 新增修改运营计划
    upsertOperationPlan(input: OperationPlanInput!): OperationPlan
    # 删除运营计划
    deleteOperationPlan(id: ID!): DeletionResponse
    # 运营计划状态切换
    changeOperationPlanState(id: ID!, state: OperationPlanState!): OperationPlan

    # 发送订阅消息
    sendMessage(phone: String!): String

    # 同步微信物流公司
    asyncWechatLogisticsCompany: String

    # 切换用户分销商
    switchoverDistributor(distributorId: ID!, customerId: ID!): String

    # 新增修改打包一口价
    upsertPackageDiscount(input: PackageDiscountInput!): PackageDiscount

    # 删除打包一口价
    softDeletePackageDiscount(id: ID!): DeletionResponse

    # 失效打包一口价
    failurePackageDiscount(id: ID!): PackageDiscount

    # 新增或者创建会员价活动
    upsertMemberPrice(input: MemberPriceInput!): MemberPrice

    # 新增或编辑会员价活动商品
    upsertMemberPriceProduct(memberPriceId: ID!, input: [MemberPriceProductInput!]!): MemberPriceProductList

    # 删除会员价活动
    deleteMemberPrice(memberPriceId: ID!): DeletionResponse

    # 删除会员价活动商品
    deleteMemberPriceProduct(memberPriceProductIds: [ID!]!): DeletionResponse

    # 删除会员价活动商品SKU
    deleteMemberPriceProductVariant(memberPriceProductVariantIds: [ID!]!): DeletionResponse

    # 设置会员卡会员价活动
    setMemberShipPlanMemberPrice(memberPriceId: ID!, membershipPlanId: ID!): MemberPrice

    # 礼品卡退卡
    returnGiftCardOrder(input: ReturnTheGiftCard!): GiftCardReturn

    # 创建或修改支付有礼活动
    upsertPaymentRewardActivity(input: PaymentRewardActivityInput!): PaymentRewardActivity
    # 删除支付有礼活动
    softDeletePaymentRewardActivity(id: ID!): DeletionResponse
    # 失效支付有礼活动
    failurePaymentRewardActivity(id: ID!): PaymentRewardActivity

    timedPublishingPageAll: String
    paymentReward(orderId: ID!): Boolean

    shoppingCreditValidPeriodAll: Boolean

    # 新增或修改分享设置
    upsertShareSetting(inputs: [ShareSettingInput!]!): [ShareSetting]

    # 个人中心组件设置
    upsertPersonalCenter(input: PersonalCenterInput!): PersonalCenter

    upsertShoppingCreditsConfig(input: ShoppingCreditsConfigInput!): ShoppingCreditsConfig

    # 创建购物金发放订阅记录
    createShoppingCreditsSubscriptionRecord(
      input: CreateSubscriptionMessageRecordInput!
    ): CreateSubscriptionMessageRecordResult

    upsertShoppingCreditsClaimActivity(input: ShoppingCreditsClaimActivityInput!): ShoppingCreditsClaimActivity
    failureShoppingCreditsClaimActivity(shoppingCreditsClaimActivityId: ID!): ShoppingCreditsClaimActivity
    softDeleteShoppingCreditsClaimActivity(shoppingCreditsClaimActivityId: ID!): DeletionResponse

    upsertActivityCountdown(input: ActivityCountdownInput!): ActivityCountdown
    deleteActivityCountdown(activityCountdownId: ID!): DeletionResponse

    # 创建更新小程序动态跳转配置
    upsertWeappDynamicRouteConfig(input: WeappDynamicRouteConfigInput!): WeappDynamicRouteConfig
  }

  # 订单项计算字段
  extend type OrderLine {
    merchantVoluntaryRefund: Int
    totalQuantity: Int
    blindBoxRefundableAmount: Int
  }

  # 订单计算字段
  extend type Order {
    # 订单项总金额
    totalPrice: Int
    # 订单总金额 = 订单项总金额 + 运费
    subTotalPrice: Int
    # 是否有支付有礼活动
    isPaymentRewardActivity: Boolean
    # 订单赠品SKU
    giftsProductVariant: [ProductVariant]
    orderBuyUserCouponId: ID
  }

  extend type Product {
    pointsExchangeInfo: PointsProduct
  }

  type MerchantVoluntaryRefund implements Node {
    id: ID!
    createdAt: DateTime!
    updatedAt: DateTime!
    order: Order
    orderLine: OrderLine
    price: Int
    reason: String
  }

  enum OrderReferralSource {
    # 全部
    all
    # 话题-(朋友圈)
    topicFriendCircle
    # 微信群
    wechatGroup
  }

  extend type ProductVariant {
    virtualTarget: VirtualTarget
    exclusionGroupsType: ProductExclusionGroupType
    exclusionGroupsTypeName: String
    isCanPurchase: Boolean
    pointExchange: PointExchange
  }

  type MembershipPlan

  union VirtualTarget = Coupon | MembershipPlan

  union ProductActivities =
      Coupon
    | PurchasePremium
    | DiscountActivity
    | FullDiscountPresent
    | PackageDiscount
    | SelectiveGiftActivity
    | ShoppingCreditsClaimActivity
    | ShoppingCreditsDeductionActivity

  # generated by generateListOptions function
  input DistributorGroupListOptions

  # generated by generateListOptions function
  input BannerListOptions

  # generated by generateListOptions function
  input CustomPageListOptions

  # generated by generateListOptions function
  input HotWordListOptions

  # generated by generateListOptions function
  input AnnouncementListOptions

  # generated by generateListOptions function
  input CouponListOptions

  # generated by generateListOptions function
  input UserCouponListOptions

  # generated by generateListOptions function
  input PurchasePremiumListOptions

  # generated by generateListOptions function
  input DistributorListOptions

  # generated by generateListOptions function
  input DiscountActivityListOptions

  # generated by generateListOptions function
  input SelectiveGiftActivityListOptions

  # generated by generateListOptions function
  input FullDiscountPresentListOptions

  # generated by generateListOptions function
  input FreeGiftListOptions

  # generated by generateListOptions function
  input GroupCustomerListOptions

  # generated by generateListOptions function
  input OperationPlanListOptions

  # generated by generateListOptions function
  input OperationPlanCustomerListOptions

  # generated by generateListOptions function
  input PackageDiscountListOptions

  # generated by generateListOptions function
  input MemberPriceListOptions

  # generated by generateListOptions function
  input MemberPriceProductListOptions

  # generated by generateListOptions function
  input MemberPriceProductVariantListOptions

  # generated by generateListOptions function
  input GiftCardReturnListOptions

  # generated by generateListOptions function
  input PaymentRewardActivityListOptions

  # generated by generateListOptions function
  input CouponBundleListOptions

  # generated by generateListOptions function
  input FirstCustomerBenefitListOptions

  # generated by generateListOptions function
  input ExclusionGroupListOptions

  # generated by generateListOptions function
  input PointsProductListOptions

  # generated by generateListOptions function
  input PointsHistoryListOptions
`;

export const shopSchemaExtensions = gql`
  # generated by generateListOptions function
  input OrderPaymentRewardCouponListOptions

  type OrderPaymentRewardCouponList implements PaginatedList {
    items: [OrderPaymentRewardCoupon!]!
    totalItems: Int!
  }

  type DistributorProductRecord implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    distributor: Distributor
    product: Product
    productStatistics: ProductStatistics
  }

  type DistributorProductRecordList implements PaginatedList {
    items: [DistributorProductRecord!]!
    totalItems: Int!
  }

  # 产品展示库存相关信息
  extend type ProductVariant {
    trackInventory: GlobalFlag!
    stockOnHand: Int!
    stockAllocated: Int!
    outOfStockThreshold: Int!
    isThereAnyStock: Boolean!
    availableStock: Int!
    useGlobalOutOfStockThreshold: Boolean!
  }

  type MemberPriceActivityAmount {
    minDiscount: Int
    maxDiscount: Int
    minMemberPriceAmount: Int
    maxMemberPriceAmount: Int
    memberPriceProductVariant: [MemberPriceProductVariantInfo]
  }

  type MemberPriceProductVariantInfo {
    memberPriceAmount: Int
    productVariantId: ID
    memberDiscount: Int
    discountType: DiscountType
  }

  # 产品展示启用状态
  extend type Product {
    enabled: Boolean!
    participatingActivities: [Promotion]
    productPurchasePermission: ProductPurchasePermission
    memberPriceActivityAmount: MemberPriceActivityAmount
    productTotalStock: Int
  }
  ${reviewProductShopApiExtension}
  ${sharedTypes}

  type AvailableCoupon {
    coupon: Coupon
    quantity: Int
  }

  extend type CouponBundle {
    availableCoupon: [AvailableCoupon]
  }

  extend type FirstCustomerBenefit {
    availableCoupon: [AvailableCoupon]
  }

  type DistributorCenterData {
    todayDistributor: DistributorStatistics
    totalDistributor: DistributorStatistics
    distributor: Distributor
  }

  type DistributorStatistics {
    # 有效订单数
    effectiveOrderNum: Int
    # 累计订单数
    orderTotal: Int
    # 有效订单金额
    accumulationAmount: Int
    # 累计订单金额
    orderTotalAmount: Int
    # 分销员ID
    distributorId: ID
    # 分销员组ID
    distributorGroupId: ID
    # 有效客户数
    effectiveCustomerNum: Int
    # 累计客户数
    customerCount: Int
    # 会员开卡总人数
    memberCustomerTotalCount: Int
    # 会员开卡有效人数
    memberCustomerEffectiveCount: Int
  }

  # generated by generateListOptions function
  input DistributorProductRecordListOptions

  # generated by generateListOptions function
  input DistributorOrderListOptions

  # generated by generateListOptions function
  input DistributorBindingListOptions

  extend type DistributorBinding {
    orderTotal: Int
    orderTotalAmount: Int
    lastOrderTime: DateTime
    perCustomerTransaction: Int
    distributorCustomerRemark: String
  }
  enum CustomerType {
    # 全部用户
    allCustomer
    # 有效用户
    effectiveCustomer
    # 下单用户
    orderCustomer
    # 无效用户
    invalidCustomer
  }

  type DistributorBindingMember {
    memberId: ID
    renewalCount: Int
    membershipPlanName: String
    customerName: String
    customerPhone: String
    customerHeadPortrait: String
    maturityAt: DateTime
    state: MemberStateInput
    maturityType: ValidityPeriodType
    isModified: Boolean
  }

  type DistributorBindingMemberList {
    items: [DistributorBindingMember!]!
    totalItems: Int!
  }

  type CheckExclusionResult {
    canCheckout: Boolean
    reason: String
  }

  extend type Query {
    checkProductInSameExclusionGroup(productIds: [ID!]!): Boolean
    checkProductCanCheckout(skuIds: [ID!]!): CheckExclusionResult

    getActiveFirstCustomerBenefit: FirstCustomerBenefit
    getFirstCustomerBenefitRecord: [AvailableCoupon]
    getReceivedCouponBundles(couponBundleId: ID!): [AvailableCoupon]

    distributorBindingMembers(
      search: String
      startDateTime: DateTime
      endDateTime: DateTime
      state: MemberStateInput
      skip: Int
      take: Int
    ): DistributorBindingMemberList
    distributorCenter(startDateTime: DateTime, endDateTime: DateTime): DistributorCenterData
    distributorPerformance(startDateTime: DateTime, endDateTime: DateTime): DistributorStatistics
    distributorsPromoteOrders(
      customerId: ID
      search: String
      startDateTime: DateTime
      endDateTime: DateTime
      options: DistributorOrderListOptions
    ): DistributorOrderList
    distributorBindingCustomers(
      customerType: CustomerType
      search: String
      startDateTime: DateTime
      endDateTime: DateTime
      options: DistributorBindingListOptions
    ): DistributorBindingList
    distributorBindingCustomerDetail(customerId: ID!): DistributorBinding
    distributorShareProducts(
      search: String
      startDateTime: DateTime
      endDateTime: DateTime
      options: DistributorProductRecordListOptions
    ): DistributorProductRecordList

    coupons(couponIds: [ID!]!): [Coupon]!

    # 查询订单对应的支付有礼信息
    orderPaymentRewardCoupons(orderId: ID!, options: OrderPaymentRewardCouponListOptions): OrderPaymentRewardCouponList
  }
  extend type Mutation {
    setDistributorBindingRemark(customerId: ID!, remark: String!): DistributorBinding
    addDistributorShareProduct(productId: ID!): DistributorProductRecord

    # 领取支付有礼的优惠券
    receivePaymentRewardCoupon(orderPaymentRewardCouponId: ID!): OrderPaymentRewardCoupon

    receiveCouponBundle(id: ID!): [UserCoupon]

    receiveFirstCustomerBenefit(id: ID!): [UserCoupon]
  }
`;

export const adminSchemaExtensions = gql`
  enum ExportType {
    order
    blindBoxOrder
    blindBoxStatistics
  }

  enum ExportTaskStatus {
    pending
    processing
    completed
    failed
  }

  type ExportTask implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    completedAt: DateTime
    status: ExportTaskStatus
    fileUrl: String
    errorMessage: String
    exportType: ExportType
    progress: Int
    queryParams: JSON
    administrator: Administrator
  }

  type ExportTaskList implements PaginatedList {
    items: [ExportTask!]!
    totalItems: Int!
  }
  input ExportTaskListOptions

  enum OrderDimension {
    product
    order
  }

  type OrderQueryParam {
    orderCode: String
    logisticsCode: String
    productName: String
    shippingPhone: String
    shippingLastName: String
    distributorName: String
    customerPhone: String
    extraState: ExtraState
    orderProductType: OrderProductType
    orderPlacedAtStart: DateTime
    orderPlacedAtEnd: DateTime
    orderDimension: OrderDimension
    isAll: Boolean
    state: String
    orderReferralSource: OrderReferralSource
  }

  input OrderQueryParamInput {
    orderCode: String
    logisticsCode: String
    productName: String
    shippingPhone: String
    shippingLastName: String
    distributorName: String
    customerPhone: String
    extraState: ExtraState
    orderProductType: OrderProductType
    orderPlacedAtStart: DateTime
    orderPlacedAtEnd: DateTime
    orderDimension: OrderDimension
    isAll: Boolean
    state: String
    orderReferralSource: OrderReferralSource
  }

  input BlindBoxOrderQueryParamInput {
    blindBoxOrderCode: String
    blindBoxName: String
    blindBoxStartTime: DateTime
    blindBoxEndTime: DateTime
    distributorName: String
    customerName: String
    customerPhone: String
    blindBoxOrderState: BlindBoxBuyStatus
  }

  input BlindBoxStatisticsQueryParamInput {
    startTime: DateTime!
    endTime: DateTime!
  }

  type ProductOptionGroupMaster implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    asset: Asset
    productOptionGroup: ProductOptionGroup
    productOptionMasters: [ProductOptionMaster]
    product: Product
  }

  type ProductOptionMaster implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    productOption: ProductOption
    asset: Asset
    productOptionGroupMaster: ProductOptionGroupMaster
  }

  input ProductOptionGroupMasterInput {
    id: ID
    assetId: ID
    productId: ID
    productOptionGroupId: ID
    productOptionMasters: [ProductOptionMasterInput]
  }

  input ProductOptionMasterInput {
    id: ID
    assetId: ID
    productOptionId: ID
  }

  # 产品展示启用状态
  extend type MerchantVoluntaryRefund {
    administrator: Administrator
  }

  extend type Product {
    participatingActivities: [Promotion]
    productPurchasePermission: ProductPurchasePermission
  }

  extend type Order {
    distributorOrder: DistributorOrder
  }

  # 用户统计数据
  extend type Customer {
    cumulativeConsumptionAmount: Int
    cumulativeConsumptionOrderNumber: Int
  }

  extend type GroupCustomer {
    administrator: Administrator
  }

  extend type FirstCustomerBenefit {
    authorizationUserCount: Int
  }

  input MerchantVoluntaryRefundInput {
    orderId: ID!
    orderLineId: ID!
    price: Int!
    count: Int
    reason: String
  }

  type CustomerStatistics {
    cumulativeConsumptionAmount: Float
    cumulativeConsumptionOrderNumber: Int
    customerUnitPrice: Float
    lastConsumptionTime: DateTime
    cumulativeRefundAmount: Float
    cumulativeRefundOrderNumber: Int
  }

  enum CodeType {
    order
    member
    giftCard
  }

  extend type Mutation {
    orderExportTaskCreate(exportType: ExportType, orderQueryParam: OrderQueryParamInput!): ExportTask
    blindBoxExportTaskCreate(exportType: ExportType, blindBoxOrderQueryParam: BlindBoxOrderQueryParamInput!): ExportTask
    blindBoxStatisticsExportTaskCreate(
      exportType: ExportType
      blindBoxStatisticsQueryParam: BlindBoxStatisticsQueryParamInput!
    ): ExportTask

    upsertExclusionGroup(input: ExclusionGroupInput!): ExclusionGroup
    softDeleteExclusionGroup(id: ID!): DeletionResponse

    batchUpdateProductEnabled(ids: [ID!]!, enabled: Boolean!): [Product]
    batchUpdateProductCollection(inputs: [UpdateCollectionInput]): [Collection]

    # 商家主动退款
    merchantVoluntaryRefund(input: MerchantVoluntaryRefundInput!): MerchantVoluntaryRefund
    # 添加分销员到组
    addDistributorToGroup(
      distributorGroupName: String!
      distributorIds: [ID!]
      distributorGroupId: ID!
    ): DistributorGroup
    # 修改或添加产品规格主图
    upsertProductOptionGroupMaster(input: ProductOptionGroupMasterInput!): ProductOptionGroupMaster

    # 同步发货信息到微信
    orderShippedToWechat(orderCode: String!, codeType: CodeType): String

    # 修改用户积分
    updateCustomerPoints(customerId: ID!, points: Int!, symbolType: SymbolType): Customer

    # 修改购物金兑换活动
    upsertShoppingCreditsDeductionActivity(
      input: ShoppingCreditsDeductionActivityInput!
    ): ShoppingCreditsDeductionActivity
    # 删除购物金兑换活动
    softDeleteShoppingCreditsDeductionActivity(shoppingCreditsDeductionActivityId: ID!): DeletionResponse
    # 失效购物金兑换活动
    failureShoppingCreditsDeductionActivity(shoppingCreditsDeductionActivityId: ID!): ShoppingCreditsDeductionActivity
  }

  extend type ProductStatistics {
    # 产品名称
    productName: String
    # 产品图片
    productPreview: String
    # 产品价格
    price: Int
    # 产品组
    collectionIds: String
  }

  type ProductDataStatistics {
    items: [ProductStatistics!]!
    totalItems: Int!
  }

  extend type Query {
    generateSmallProgramLinkByPath(path: String!): String

    getDownloadLink(exportTaskId: ID!): String
    exportTasks(options: ExportTaskListOptions): ExportTaskList

    # 查询购买的优惠券可退数量
    getRefundableCount(orderId: ID!): Int
    # 查询产品规格主图
    productOptionGroupMasters(productId: ID!): ProductOptionGroupMaster
    distributorOrder(orderId: ID): DistributorOrder
    # 用户交易统计
    customerStatistics(customerId: ID!): CustomerStatistics
    dataStatistics(startDate: DateTime, endDate: DateTime): DataStatistics
    # 销售统计
    salesStatistics(startDate: DateTime, endDate: DateTime): SalesStatistics
    # 实时概括
    realTimeStatistics(startDate: DateTime, endDate: DateTime): RealTimeDataStatistics
    # 数据转化分析
    conversionAnalysis(startDate: DateTime, endDate: DateTime): ConversionAnalysis
    # 趋势统计
    trendStatistics(type: TrendType, dateTime: DateTime, trendSource: TrendSource): TrendStatistics
    # 产品数据统计
    productDataStatistics(
      collectionId: ID!
      startTime: DateTime!
      endTime: DateTime!
      productName: String
      take: Int
      skip: Int
      sortName: String
      sortType: String
    ): ProductDataStatistics
    # SKU数据统计
    productVariantDataStatistics(
      productId: ID!
      startTime: DateTime
      endTime: DateTime
      take: Int
      skip: Int
      sortName: String
      sortType: String
    ): ProductVariantDataStatistics

    # 获取可分配的分销员 不在分销员组中的
    getDistributors(notInDistributorGroupId: ID, phone: String, options: DistributorListOptions): DistributorList

    coupons(options: CouponListOptions): CouponList

    # 获取分销组详情
    distributorsGroup(distributorsGroupId: ID!): DistributorGroup

    orderPreview(start: DateTime!, end: DateTime!): OrderList!
  }
  ${sharedTypes}
  ${reviewProductAdminApiExtension}
`;
