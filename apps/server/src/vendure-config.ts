import {AfterSalePlugin} from '@scmally/after-sale';
import {CompPlugin} from '@scmally/comp';
import {CompBoxPlugin} from '@scmally/comp-box';
import {CommonPlugin} from '@scmally/ecommerce-common';
import {ForumPlugin} from '@scmally/forum';
import {GatherWaterPondPlugin} from '@scmally/gather-water-pond';
import {KvsPlugin} from '@scmally/kvs';
import {MemberPlugin} from '@scmally/member';
import {MinioPlugin} from '@scmally/minio';
import {QiYuPlugin} from '@scmally/qi-yu';
import {RedLockPlugin} from '@scmally/red-lock';
import {SubscriptionPlugin} from '@scmally/subscription';
import {VirtualCurrencyPlugin} from '@scmally/virtual-currency';
import {WangDianTongPlugin} from '@scmally/wang-dian-tong';
import {WeChatPlugin} from '@scmally/wechat';
import {YouZanYunPlugin} from '@scmally/youzanyun';
import {AdminUiPlugin} from '@vendure/admin-ui-plugin';
import {AssetServerPlugin} from '@vendure/asset-server-plugin';
import {DefaultJobQueuePlugin, DefaultSearchPlugin, VendureConfig, dummyPaymentHandler} from '@vendure/core';
import {json} from 'body-parser';
import 'dotenv/config';
import path from 'path';
import {FavoritesPlugin} from 'vendure-favorites-plugin';
import {customConfigureS3AssetStorage} from './custom-s3-asset-storage-strategy';
import {SimpleFileLogger} from './simple-file-logger';
// import {VideoShopPlugin} from '@scmally/video-shop';
//TODO 所有环境都可查看静态文件（接口文档和本地资源图片）
const IS_DEV = process.env.APP_ENV === 'dev' || process.env.APP_ENV === 'test' || process.env.APP_ENV === 'staging';

export const config: VendureConfig = {
  apiOptions: {
    port: Number(process.env.PORT || 3000),
    adminApiPath: 'admin-api',
    shopApiPath: 'shop-api',
    shopListQueryLimit: 1000,
    // The following options are useful in development mode,
    // but are best turned off for production for security
    // reasons.
    ...(IS_DEV
      ? {
          adminApiPlayground: {
            settings: {'request.credentials': 'include'} as any,
          },
          adminApiDebug: true,
          shopApiPlayground: {
            settings: {'request.credentials': 'include'} as any,
          },
          shopApiDebug: true,
        }
      : {}),
    middleware: [
      {
        handler: json({limit: '100mb'}),
        route: '*',
        beforeListen: true,
      },
    ],
  },
  authOptions: {
    tokenMethod: 'bearer',
    sessionDuration: '1d',
    superadminCredentials: {
      identifier: process.env.SUPERADMIN_USERNAME,
      password: process.env.SUPERADMIN_PASSWORD,
    },
    cookieOptions: {
      secret: process.env.COOKIE_SECRET,
    },
  },
  entityOptions: {
    channelCacheTtl: 1000 * 60 * 60 * 24,
    zoneCacheTtl: 1000 * 60 * 60 * 24,
    taxRateCacheTtl: 1000 * 60 * 60 * 24,
  },
  // logger: new DefaultLogger({level: LogLevel.Debug}),
  logger: new SimpleFileLogger('../../logs'),
  dbConnectionOptions: {
    type: 'mysql',
    // See the README.md "Migrations" section for an explanation of
    // the `synchronize` and `migrations` options.
    synchronize: false,
    migrations: [path.join(__dirname, './migrations/*.+(js|ts)')],
    logging: false,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: +process.env.DB_PORT,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    timezone: '+08:00',
    charset: 'utf8mb4',
    poolSize: 200,
    // 添加会启动报错
    cache: {
      type: 'redis',
      options: {
        host: process.env.REDIS_HOST,
        port: +(process.env.REDIS_PORT ?? 6379),
        db: +(process.env.REDIS_DB ?? 10),
      },
      duration: 1000 * 60 * 60 * 24,
    },
  },
  paymentOptions: {
    paymentMethodHandlers: [dummyPaymentHandler],
  },
  // When adding or altering custom field definitions, the database will
  // need to be updated. See the "Migrations" section in README.md.
  customFields: {},
  jobQueueOptions: {
    activeQueues: ['同步产品', '同步用户累计金额'],
  },
  plugins: [
    // MigrationV2Plugin,
    AssetServerPlugin.init({
      route: 'assets',
      // assetUploadDir: path.join(__dirname, '../static/assets'),
      assetUploadDir: path.join(__dirname, process.env.LOCAL_FILE_STORAGE_PATH || '../../../../static/assets'),
      // For local dev, the correct value for assetUrlPrefix should
      // be guessed correctly, but for production it will usually need
      // to be set manually to match your production url.
      // assetUrlPrefix: IS_DEV ? undefined : 'https://www.my-shop.com/assets',
      // assetUrlPrefix: `https://${process.env.MINIO_ENDPOINT_CDN}/${process.env.ADMIN_BUCKET}/`,
      assetUrlPrefix: `https://${process.env.ASSETS_PREFIX}/${process.env.ASSETS_ROUTE}/`,
      storageStrategyFactory: customConfigureS3AssetStorage({
        bucket: process.env.ADMIN_BUCKET || '',
        credentials: {
          accessKeyId: process.env.MINIO_ACCESS_KEY || '',
          secretAccessKey: process.env.MINIO_SECRET_KEY || '',
        },
        nativeS3Configuration: {
          endpoint: `https://${process.env.MINIO_ENDPOINT}` || 'http://localhost:3000',
          s3ForcePathStyle: true,
          signatureVersion: 'v4',
        },
      }),
    }),

    DefaultJobQueuePlugin.init({
      useDatabaseForBuffer: true,
      pollInterval: queueName => {
        if (queueName === '同步产品') {
          return 1000 * 60 * 10;
        } else if (queueName === '优惠券发放') {
          return 1000 * 60;
        } else if (queueName === 'apply-collection-filters') {
          return 1000;
        } else if (queueName === 'update-search-index') {
          return 1000;
        } else if (queueName === '同步用户累计金额') {
          return 1000 * 60;
        }
        // else if (queueName === 'send-email') {
        //   return 1000 * 60;
        // }
        return 1000;
      },
    }),
    DefaultSearchPlugin.init({bufferUpdates: false, indexStockStatus: true}),
    RedLockPlugin,
    VirtualCurrencyPlugin,
    FavoritesPlugin,
    CommonPlugin,
    SubscriptionPlugin,
    WeChatPlugin,
    KvsPlugin,
    MinioPlugin,
    CompPlugin,
    CompBoxPlugin,
    AfterSalePlugin,
    MemberPlugin,
    ForumPlugin,
    GatherWaterPondPlugin,
    QiYuPlugin,
    YouZanYunPlugin.init({isDisableCron: true}),
    WangDianTongPlugin,
    AdminUiPlugin.init({
      route: 'admin',
      port: 3002,
    }),
  ],
};
