/* eslint-disable @typescript-eslint/no-explicit-any */
import {AfterSalePlugin} from '@scmally/after-sale';
import {CompPlugin} from '@scmally/comp';
import {CompBoxPlugin} from '@scmally/comp-box';
import {CommonPlugin} from '@scmally/ecommerce-common';
import {ForumPlugin} from '@scmally/forum';
import {GatherWaterPondPlugin} from '@scmally/gather-water-pond';
import {KvsPlugin} from '@scmally/kvs';
import {MemberPlugin} from '@scmally/member';
import {MinioPlugin} from '@scmally/minio';
import {QiYuPlugin} from '@scmally/qi-yu';
import {RedLockPlugin} from '@scmally/red-lock';
import {SubscriptionPlugin} from '@scmally/subscription';
import {VirtualCurrencyPlugin} from '@scmally/virtual-currency';
import {WangDianTongPlugin} from '@scmally/wang-dian-tong';
import {WeChatPlugin} from '@scmally/wechat';
import {YouZanYunPlugin} from '@scmally/youzanyun';
import {DefaultJobQueuePlugin, DefaultSearchPlugin, VendureConfig, dummyPaymentHandler} from '@vendure/core';
import 'dotenv/config';
import path from 'path';
import {FavoritesPlugin} from 'vendure-favorites-plugin';
import {SimpleFileLogger} from './simple-file-logger';
// import {VideoShopPlugin} from '@scmally/video-shop';
//TODO 所有环境都可查看静态文件（接口文档和本地资源图片）
const IS_DEV = false;
const isDisableCron = true;
export const config: VendureConfig = {
  apiOptions: {
    port: Number(process.env.PORT || 3000),
    adminApiPath: 'admin-api',
    shopApiPath: 'shop-api',
    // The following options are useful in development mode,
    // but are best turned off for production for security
    // reasons.
    ...(IS_DEV
      ? {
          adminApiPlayground: {
            settings: {'request.credentials': 'include'} as any,
          },
          adminApiDebug: true,
          shopApiPlayground: {
            settings: {'request.credentials': 'include'} as any,
          },
          shopApiDebug: true,
        }
      : {}),
  },
  authOptions: {
    tokenMethod: 'bearer',
    sessionDuration: '1d',
    superadminCredentials: {
      identifier: process.env.SUPERADMIN_USERNAME,
      password: process.env.SUPERADMIN_PASSWORD,
    },
    cookieOptions: {
      secret: process.env.COOKIE_SECRET,
    },
  },
  logger: new SimpleFileLogger('../../logs'),
  dbConnectionOptions: {
    type: 'mysql',
    // See the README.md "Migrations" section for an explanation of
    // the `synchronize` and `migrations` options.
    synchronize: false,
    migrations: [path.join(__dirname, './migrations/*.+(js|ts)')],
    logging: false,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: +process.env.DB_PORT,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    timezone: '+08:00',
    charset: 'utf8mb4',
    poolSize: 200,
    // 添加会启动报错
    cache: {
      type: 'redis',
      options: {
        host: process.env.REDIS_HOST,
        port: +(process.env.REDIS_PORT ?? 6379),
        db: +(process.env.REDIS_DB ?? 10),
      },
      duration: 1000 * 60 * 60 * 24,
    },
  },
  paymentOptions: {
    paymentMethodHandlers: [dummyPaymentHandler],
  },
  // When adding or altering custom field definitions, the database will
  // need to be updated. See the "Migrations" section in README.md.
  customFields: {},
  jobQueueOptions: {
    activeQueues: ['apply-collection-filters', 'update-search-index', '优惠券发放'],
  },
  plugins: [
    DefaultJobQueuePlugin.init({
      useDatabaseForBuffer: true,
    }),
    DefaultSearchPlugin.init({bufferUpdates: false, indexStockStatus: true}),
    RedLockPlugin,
    VirtualCurrencyPlugin,
    FavoritesPlugin,
    CommonPlugin.init({isDisableCron}),
    SubscriptionPlugin.init({isDisableCron}),
    WeChatPlugin,
    KvsPlugin,
    MinioPlugin,
    CompPlugin,
    CompBoxPlugin,
    AfterSalePlugin.init({isDisableCron}),
    MemberPlugin.init({isDisableCron}),
    ForumPlugin.init({isDisableCron}),
    GatherWaterPondPlugin.init({isDisableCron}),
    QiYuPlugin,
    YouZanYunPlugin.init({isDisableCron: false}),
    WangDianTongPlugin.init({isDisableCron}),
    // VideoShopPlugin.init({isDisableCron}),
  ],
};
